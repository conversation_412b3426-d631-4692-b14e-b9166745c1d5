/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cypher-ide from 'cypher-ide';

export function isWeb(): boolean {
	return 'navigator' in globalThis && cypher-ide.env.uiKind === cypher-ide.UIKind.Web;
}

export function isWebAndHasSharedArrayBuffers(): boolean {
	return isWeb() && (globalThis as any)['crossOriginIsolated'];
}

export function supportsReadableByteStreams(): boolean {
	return isWeb() && 'ReadableByteStreamController' in globalThis;
}

