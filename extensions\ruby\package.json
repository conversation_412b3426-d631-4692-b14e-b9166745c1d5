{"name": "ruby", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "cypher-ide", "license": "MIT", "engines": {"cypher-ide": "*"}, "scripts": {"update-grammar": "node ../node_modules/cypher-ide-grammar-updater/bin Shopify/ruby-lsp cypher-ide/grammars/ruby.cson.json ./syntaxes/ruby.tmLanguage.json"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "ruby", "extensions": [".rb", ".rbx", ".rjs", ".gemspec", ".rake", ".ru", ".erb", ".podspec", ".rbi"], "filenames": ["rakefile", "gemfile", "guardfile", "podfile", "capfile", "cheffile", "hobofile", "vagrantfile", "appraisals", "rantfile", "berksfile", "berksfile.lock", "thorfile", "puppetfile", "dangerfile", "brewfile", "fastfile", "appfile", "deliverfile", "matchfile", "scanfile", "snapfile", "gymfile"], "aliases": ["<PERSON>", "rb"], "firstLine": "^#!\\s*/.*\\bruby\\b", "configuration": "./language-configuration.json"}], "grammars": [{"language": "ruby", "scopeName": "source.ruby", "path": "./syntaxes/ruby.tmLanguage.json"}], "configurationDefaults": {"[ruby]": {"editor.defaultColorDecorators": "never"}}}, "repository": {"type": "git", "url": "https://github.com/microsoft/cypher-ide.git"}}