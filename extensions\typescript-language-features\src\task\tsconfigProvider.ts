/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cypher-ide from 'cypher-ide';

export interface TSConfig {
	readonly uri: cypher-ide.Uri;
	readonly fsPath: string;
	readonly posixPath: string;
	readonly workspaceFolder?: cypher-ide.WorkspaceFolder;
}

export class TsConfigProvider {
	public async getConfigsForWorkspace(token: cypher-ide.CancellationToken): Promise<Iterable<TSConfig>> {
		if (!cypher-ide.workspace.workspaceFolders) {
			return [];
		}

		const configs = new Map<string, TSConfig>();
		for (const config of await this.findConfigFiles(token)) {
			const root = cypher-ide.workspace.getWorkspaceFolder(config);
			if (root) {
				configs.set(config.fsPath, {
					uri: config,
					fsPath: config.fsPath,
					posixPath: config.path,
					workspaceFolder: root
				});
			}
		}
		return configs.values();
	}

	private async findConfigFiles(token: cypher-ide.CancellationToken): Promise<cypher-ide.Uri[]> {
		return await cypher-ide.workspace.findFiles('**/tsconfig*.json', '**/{node_modules,.*}/**', undefined, token);
	}
}
