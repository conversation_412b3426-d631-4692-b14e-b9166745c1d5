/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cypher-ide from 'cypher-ide';

const Logger = cypher-ide.window.createOutputChannel(cypher-ide.l10n.t('Microsoft Authentication'), { log: true });
export default Logger;
