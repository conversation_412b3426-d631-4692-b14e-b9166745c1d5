/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { basename, dirname } from 'path';
import * as cypher-ide from 'cypher-ide';
import { Logger } from '../logging/logger';

export class MemFs implements cypher-ide.FileSystemProvider {

	private readonly root = new FsDirectoryEntry(
		new Map(),
		0,
		0,
	);

	constructor(
		private readonly id: string,
		private readonly logger: Logger,
	) { }

	stat(uri: cypher-ide.Uri): cypher-ide.FileStat {
		this.logger.trace(`MemFs.stat ${this.id}. uri: ${uri}`);
		const entry = this.getEntry(uri);
		if (!entry) {
			throw cypher-ide.FileSystemError.FileNotFound();
		}

		return entry;
	}

	readDirectory(uri: cypher-ide.Uri): [string, cypher-ide.FileType][] {
		this.logger.trace(`MemFs.readDirectory ${this.id}. uri: ${uri}`);

		const entry = this.getEntry(uri);
		if (!entry) {
			throw cypher-ide.FileSystemError.FileNotFound();
		}
		if (!(entry instanceof FsDirectoryEntry)) {
			throw cypher-ide.FileSystemError.FileNotADirectory();
		}

		return Array.from(entry.contents.entries(), ([name, entry]) => [name, entry.type]);
	}

	readFile(uri: cypher-ide.Uri): Uint8Array {
		this.logger.trace(`MemFs.readFile ${this.id}. uri: ${uri}`);

		const entry = this.getEntry(uri);
		if (!entry) {
			throw cypher-ide.FileSystemError.FileNotFound();
		}

		if (!(entry instanceof FsFileEntry)) {
			throw cypher-ide.FileSystemError.FileIsADirectory(uri);
		}

		return entry.data;
	}

	writeFile(uri: cypher-ide.Uri, content: Uint8Array, { create, overwrite }: { create: boolean; overwrite: boolean }): void {
		this.logger.trace(`MemFs.writeFile ${this.id}. uri: ${uri}`);

		const dir = this.getParent(uri);

		const fileName = basename(uri.path);
		const dirContents = dir.contents;

		const time = Date.now() / 1000;
		const entry = dirContents.get(basename(uri.path));
		if (!entry) {
			if (create) {
				dirContents.set(fileName, new FsFileEntry(content, time, time));
				this._emitter.fire([{ type: cypher-ide.FileChangeType.Created, uri }]);
			} else {
				throw cypher-ide.FileSystemError.FileNotFound();
			}
		} else {
			if (entry instanceof FsDirectoryEntry) {
				throw cypher-ide.FileSystemError.FileIsADirectory(uri);
			}

			if (overwrite) {
				entry.mtime = time;
				entry.data = content;
				this._emitter.fire([{ type: cypher-ide.FileChangeType.Changed, uri }]);
			} else {
				throw cypher-ide.FileSystemError.NoPermissions('overwrite option was not passed in');
			}
		}
	}

	rename(_oldUri: cypher-ide.Uri, _newUri: cypher-ide.Uri, _options: { overwrite: boolean }): void {
		throw new Error('not implemented');
	}

	delete(uri: cypher-ide.Uri): void {
		try {
			const dir = this.getParent(uri);
			dir.contents.delete(basename(uri.path));
			this._emitter.fire([{ type: cypher-ide.FileChangeType.Deleted, uri }]);
		} catch (e) { }
	}

	createDirectory(uri: cypher-ide.Uri): void {
		this.logger.trace(`MemFs.createDirectory ${this.id}. uri: ${uri}`);

		const dir = this.getParent(uri);
		const now = Date.now() / 1000;
		dir.contents.set(basename(uri.path), new FsDirectoryEntry(new Map(), now, now));
	}

	private getEntry(uri: cypher-ide.Uri): FsEntry | undefined {
		// TODO: have this throw FileNotFound itself?
		// TODO: support configuring case sensitivity
		let node: FsEntry = this.root;
		for (const component of uri.path.split('/')) {
			if (!component) {
				// Skip empty components (root, stuff between double slashes,
				// trailing slashes)
				continue;
			}

			if (!(node instanceof FsDirectoryEntry)) {
				// We're looking at a File or such, so bail.
				return;
			}

			const next = node.contents.get(component);
			if (!next) {
				// not found!
				return;
			}

			node = next;
		}
		return node;
	}

	private getParent(uri: cypher-ide.Uri): FsDirectoryEntry {
		const dir = this.getEntry(uri.with({ path: dirname(uri.path) }));
		if (!dir) {
			throw cypher-ide.FileSystemError.FileNotFound();
		}
		if (!(dir instanceof FsDirectoryEntry)) {
			throw cypher-ide.FileSystemError.FileNotADirectory();
		}
		return dir;
	}

	// --- manage file events

	private readonly _emitter = new cypher-ide.EventEmitter<cypher-ide.FileChangeEvent[]>();

	readonly onDidChangeFile: cypher-ide.Event<cypher-ide.FileChangeEvent[]> = this._emitter.event;
	private readonly watchers = new Map<string, Set<Symbol>>;

	watch(resource: cypher-ide.Uri): cypher-ide.Disposable {
		if (!this.watchers.has(resource.path)) {
			this.watchers.set(resource.path, new Set());
		}
		const sy = Symbol(resource.path);
		return new cypher-ide.Disposable(() => {
			const watcher = this.watchers.get(resource.path);
			if (watcher) {
				watcher.delete(sy);
				if (!watcher.size) {
					this.watchers.delete(resource.path);
				}
			}
		});
	}
}

class FsFileEntry {
	readonly type = cypher-ide.FileType.File;

	get size(): number {
		return this.data.length;
	}

	constructor(
		public data: Uint8Array,
		public readonly ctime: number,
		public mtime: number,
	) { }
}

class FsDirectoryEntry {
	readonly type = cypher-ide.FileType.Directory;

	get size(): number {
		return [...this.contents.values()].reduce((acc: number, entry: FsEntry) => acc + entry.size, 0);
	}

	constructor(
		public readonly contents: Map<string, FsEntry>,
		public readonly ctime: number,
		public readonly mtime: number,
	) { }
}

type FsEntry = FsFileEntry | FsDirectoryEntry;
