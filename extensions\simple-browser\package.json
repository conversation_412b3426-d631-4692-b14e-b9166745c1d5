{"name": "simple-browser", "displayName": "%displayName%", "description": "%description%", "enabledApiProposals": ["externalUriOpener"], "version": "1.0.0", "icon": "media/icon.png", "publisher": "cypher-ide", "license": "MIT", "aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "engines": {"cypher-ide": "^1.70.0"}, "main": "./out/extension", "browser": "./dist/browser/extension", "categories": ["Other"], "extensionKind": ["ui", "workspace"], "activationEvents": ["onCommand:simpleBrowser.api.open", "onOpenExternalUri:http", "onOpenExternalUri:https", "onWebviewPanel:simpleBrowser.view"], "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": true}}, "contributes": {"commands": [{"command": "simpleBrowser.show", "title": "Show", "category": "Simple Browser"}], "configuration": [{"title": "Simple Browser", "properties": {"simpleBrowser.focusLockIndicator.enabled": {"type": "boolean", "default": true, "title": "Focus Lock Indicator Enabled", "description": "%configuration.focusLockIndicator.enabled.description%"}}}]}, "scripts": {"compile": "gulp compile-extension:markdown-language-features && npm run build-preview", "watch": "npm run build-preview && gulp watch-extension:markdown-language-features", "cypher-ide:prepublish": "npm run build-ext && npm run build-preview", "build-ext": "node ../../node_modules/gulp/bin/gulp.js --gulpfile ../../build/gulpfile.extensions.js compile-extension:markdown-language-features ./tsconfig.json", "build-preview": "node ./esbuild-preview", "compile-web": "npx webpack-cli --config extension-browser.webpack.config --mode none", "watch-web": "npx webpack-cli --config extension-browser.webpack.config --mode none --watch --info-verbosity verbose"}, "dependencies": {"@cypher-ide/extension-telemetry": "^0.9.8"}, "devDependencies": {"@types/cypher-ide-webview": "^1.57.0", "@cypher-ide/codicons": "^0.0.36"}, "repository": {"type": "git", "url": "https://github.com/microsoft/cypher-ide.git"}}