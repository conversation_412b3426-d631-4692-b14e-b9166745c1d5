/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cypher-ide from 'cypher-ide';

export async function exists(resource: cypher-ide.Uri): Promise<boolean> {
	try {
		const stat = await cypher-ide.workspace.fs.stat(resource);
		// stat.type is an enum flag
		return !!(stat.type & cypher-ide.FileType.File);
	} catch {
		return false;
	}
}

export function looksLikeAbsoluteWindowsPath(path: string): boolean {
	return /^[a-zA-Z]:[\/\\]/.test(path);
}
