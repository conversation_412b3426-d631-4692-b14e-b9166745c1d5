/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cypher-ide from 'cypher-ide';
import { Command } from './commandManager';

export class EnableTsgoCommand implements Command {
	public readonly id = 'typescript.experimental.enableTsgo';

	public async execute(): Promise<void> {
		await updateTsgoSetting(true);
	}
}

export class DisableTsgoCommand implements Command {
	public readonly id = 'typescript.experimental.disableTsgo';

	public async execute(): Promise<void> {
		await updateTsgoSetting(false);
	}
}

/**
 * Updates the TypeScript Go setting and reloads extension host.
 * @param enable Whether to enable or disable TypeScript Go
 */
async function updateTsgoSetting(enable: boolean): Promise<void> {
	const tsgoExtension = cypher-ide.extensions.getExtension('typescript.typescript-lsp');
	// Error if the TypeScript Go extension is not installed with a button to open the GitHub repo
	if (!tsgoExtension) {
		const selection = await cypher-ide.window.showErrorMessage(
			cypher-ide.l10n.t('The TypeScript Go extension is not installed.'),
			{
				title: cypher-ide.l10n.t('Open on GitHub'),
				isCloseAffordance: true,
			}
		);

		if (selection) {
			await cypher-ide.env.openExternal(cypher-ide.Uri.parse('https://github.com/microsoft/typescript-go'));
		}
	}

	const tsConfig = cypher-ide.workspace.getConfiguration('typescript');
	const currentValue = tsConfig.get<boolean>('experimental.useTsgo', false);
	if (currentValue === enable) {
		return;
	}

	// Determine the target scope for the configuration update
	let target = cypher-ide.ConfigurationTarget.Global;
	const inspect = tsConfig.inspect<boolean>('experimental.useTsgo');
	if (inspect?.workspaceValue !== undefined) {
		target = cypher-ide.ConfigurationTarget.Workspace;
	} else if (inspect?.workspaceFolderValue !== undefined) {
		target = cypher-ide.ConfigurationTarget.WorkspaceFolder;
	} else {
		// If setting is not defined yet, use the same scope as typescript-go.executablePath
		const tsgoConfig = cypher-ide.workspace.getConfiguration('typescript-go');
		const tsgoInspect = tsgoConfig.inspect<string>('executablePath');

		if (tsgoInspect?.workspaceValue !== undefined) {
			target = cypher-ide.ConfigurationTarget.Workspace;
		} else if (tsgoInspect?.workspaceFolderValue !== undefined) {
			target = cypher-ide.ConfigurationTarget.WorkspaceFolder;
		}
	}

	// Update the setting, restart the extension host, and enable the TypeScript Go extension
	await tsConfig.update('experimental.useTsgo', enable, target);
	await cypher-ide.commands.executeCommand('workbench.action.restartExtensionHost');
}
