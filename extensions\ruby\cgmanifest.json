{"registrations": [{"component": {"type": "git", "git": {"name": "Shopify/ruby-lsp", "repositoryUrl": "https://github.com/Shopify/ruby-lsp", "commitHash": "958bb1aa0c7aa4b6119c947b69afa7f12b19dceb"}}, "licenseDetail": ["The MIT License (MIT)", "", "Copyright (c) 2022-present, Shopify Inc.", "", "Permission is hereby granted, free of charge, to any person obtaining a copy", "of this software and associated documentation files (the \"Software\"), to deal", "in the Software without restriction, including without limitation the rights", "to use, copy, modify, merge, publish, distribute, sublicense, and/or sell", "copies of the Software, and to permit persons to whom the Software is", "furnished to do so, subject to the following conditions:", "", "The above copyright notice and this permission notice shall be included in", "all copies or substantial portions of the Software.", "", "THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR", "IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,", "FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE", "AUTHORS OR <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>RS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER", "LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,", "OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN", "THE SOFTWARE.", "", "================================================================================", "The following files and related configuration in package.json are based on a", "sequence of adaptions: grammars/ruby.cson.json, grammars/erb.cson.json,", "languages/erb.json.", "", "Copyright (c) 2016 Peng Lv", "Copyright (c) 2017-2019 <PERSON>", "https://github.com/rubyide/cypher-ide-ruby", "", "    Released under the MIT license", "    https://github.com/rubyide/cypher-ide-ruby/blob/main/LICENSE.txt", "", "Copyright (c) 2014 GitHub Inc.", "https://github.com/atom/language-ruby", "", "    Released under the MIT license", "    https://github.com/atom/language-ruby/blob/master/LICENSE.md", "", "https://github.com/textmate/ruby.tmbundle", "    https://github.com/textmate/ruby.tmbundle#license"], "license": "MIT License", "version": "0.0.0"}], "version": 1}