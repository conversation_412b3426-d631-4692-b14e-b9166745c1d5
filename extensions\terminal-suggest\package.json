{"name": "terminal-suggest", "publisher": "cypher-ide", "displayName": "%displayName%", "description": "%description%", "version": "1.0.1", "private": true, "license": "MIT", "icon": "./src/media/icon.png", "engines": {"cypher-ide": "^1.95.0"}, "categories": ["Other"], "enabledApiProposals": ["terminalCompletionProvider", "terminalShellEnv"], "scripts": {"compile": "npx gulp compile-extension:terminal-suggest", "watch": "npx gulp watch-extension:terminal-suggest", "pull-zshbuiltins": "ts-node ./scripts/pullZshBuiltins.ts", "pull-fishbuiltins": "ts-node ./scripts/pullFishBuiltins.ts"}, "main": "./out/terminalSuggestMain", "activationEvents": ["onTerminalCompletionsRequested"], "repository": {"type": "git", "url": "https://github.com/microsoft/cypher-ide.git"}}