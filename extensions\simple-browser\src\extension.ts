/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cypher-ide from 'cypher-ide';
import { SimpleBrowserManager } from './simpleBrowserManager';
import { SimpleBrowserView } from './simpleBrowserView';

declare class URL {
	constructor(input: string, base?: string | URL);
	hostname: string;
}

const openApiCommand = 'simpleBrowser.api.open';
const showCommand = 'simpleBrowser.show';

const enabledHosts = new Set<string>([
	'localhost',
	// localhost IPv4
	'127.0.0.1',
	// localhost IPv6
	'[0:0:0:0:0:0:0:1]',
	'[::1]',
	// all interfaces IPv4
	'0.0.0.0',
	// all interfaces IPv6
	'[0:0:0:0:0:0:0:0]',
	'[::]'
]);

const openerId = 'simpleBrowser.open';

export function activate(context: cypher-ide.ExtensionContext) {

	const manager = new SimpleBrowserManager(context.extensionUri);
	context.subscriptions.push(manager);

	context.subscriptions.push(cypher-ide.window.registerWebviewPanelSerializer(SimpleBrowserView.viewType, {
		deserializeWebviewPanel: async (panel, state) => {
			manager.restore(panel, state);
		}
	}));

	context.subscriptions.push(cypher-ide.commands.registerCommand(showCommand, async (url?: string) => {
		if (!url) {
			url = await cypher-ide.window.showInputBox({
				placeHolder: cypher-ide.l10n.t("https://example.com"),
				prompt: cypher-ide.l10n.t("Enter url to visit")
			});
		}

		if (url) {
			manager.show(url);
		}
	}));

	context.subscriptions.push(cypher-ide.commands.registerCommand(openApiCommand, (url: cypher-ide.Uri, showOptions?: {
		preserveFocus?: boolean;
		viewColumn: cypher-ide.ViewColumn;
	}) => {
		manager.show(url, showOptions);
	}));

	context.subscriptions.push(cypher-ide.window.registerExternalUriOpener(openerId, {
		canOpenExternalUri(uri: cypher-ide.Uri) {
			// We have to replace the IPv6 hosts with IPv4 because URL can't handle IPv6.
			const originalUri = new URL(uri.toString(true));
			if (enabledHosts.has(originalUri.hostname)) {
				return isWeb()
					? cypher-ide.ExternalUriOpenerPriority.Default
					: cypher-ide.ExternalUriOpenerPriority.Option;
			}

			return cypher-ide.ExternalUriOpenerPriority.None;
		},
		openExternalUri(resolveUri: cypher-ide.Uri) {
			return manager.show(resolveUri, {
				viewColumn: cypher-ide.window.activeTextEditor ? cypher-ide.ViewColumn.Beside : cypher-ide.ViewColumn.Active
			});
		}
	}, {
		schemes: ['http', 'https'],
		label: cypher-ide.l10n.t("Open in simple browser"),
	}));
}

function isWeb(): boolean {
	// @ts-expect-error
	return typeof navigator !== 'undefined' && cypher-ide.env.uiKind === cypher-ide.UIKind.Web;
}
