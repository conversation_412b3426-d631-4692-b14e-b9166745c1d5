/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import * as cypher-ide from 'cypher-ide';

export interface IMergeRegion {
	name: string;
	header: cypher-ide.Range;
	content: cypher-ide.Range;
	decoratorContent: cypher-ide.Range;
}

export const enum CommitType {
	Current,
	Incoming,
	Both
}

export interface IExtensionConfiguration {
	enableCodeLens: boolean;
	enableDecorations: boolean;
	enableEditorOverview: boolean;
}

export interface IDocumentMergeConflict extends IDocumentMergeConflictDescriptor {
	commitEdit(type: CommitType, editor: cypher-ide.TextEditor, edit?: cypher-ide.TextEditorEdit): Thenable<boolean>;
	applyEdit(type: CommitType, document: cypher-ide.TextDocument, edit: { replace(range: cypher-ide.Range, newText: string): void }): void;
}

export interface IDocumentMergeConflictDescriptor {
	range: cypher-ide.Range;
	current: IMergeRegion;
	incoming: IMergeRegion;
	commonAncestors: IMergeRegion[];
	splitter: cypher-ide.Range;
}

export interface IDocumentMergeConflictTracker {
	getConflicts(document: cypher-ide.TextDocument): PromiseLike<IDocumentMergeConflict[]>;
	isPending(document: cypher-ide.TextDocument): boolean;
	forget(document: cypher-ide.TextDocument): void;
}

export interface IDocumentMergeConflictTrackerService {
	createTracker(origin: string): IDocumentMergeConflictTracker;
	forget(document: cypher-ide.TextDocument): void;
}
