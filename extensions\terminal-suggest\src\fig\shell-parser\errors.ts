/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { createErrorInstance } from '../shared/errors';

export const SubstituteAliasError = createErrorInstance('SubstituteAliasError');
export const ConvertCommandError = createErrorInstance('ConvertCommandError');
