/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

export const addMissingAwait = 'addMissingAwait';
export const addMissingNewOperator = 'addMissingNewOperator';
export const addMissingOverride = 'fixOverrideModifier';
export const annotateWithTypeFromJSDoc = 'annotateWithTypeFromJSDoc';
export const awaitInSyncFunction = 'fixAwaitInSyncFunction';
export const classDoesntImplementInheritedAbstractMember = 'fixClassDoesntImplementInheritedAbstractMember';
export const classIncorrectlyImplementsInterface = 'fixClassIncorrectlyImplementsInterface';
export const constructorForDerivedNeedSuperCall = 'constructorForDerivedNeedSuperCall';
export const extendsInterfaceBecomesImplements = 'extendsInterfaceBecomesImplements';
export const fixImport = 'import';
export const forgottenThisPropertyAccess = 'forgottenThisPropertyAccess';
export const removeUnnecessaryAwait = 'removeUnnecessaryAwait';
export const spelling = 'spelling';
export const inferFromUsage = 'inferFromUsage';
export const addNameToNamelessParameter = 'addNameToNamelessParameter';
export const fixMissingFunctionDeclaration = 'fixMissingFunctionDeclaration';
export const fixClassDoesntImplementInheritedAbstractMember = 'fixClassDoesntImplementInheritedAbstractMember';
export const unreachableCode = 'fixUnreachableCode';
export const unusedIdentifier = 'unusedIdentifier';
