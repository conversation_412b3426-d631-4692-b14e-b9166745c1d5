{
	"extends": "../tsconfig.base.json",
	"compilerOptions": {
		"outDir": "./out",
		"esModuleInterop": true,
		"experimentalDecorators": true,
		"types": [
			"node"
		]
	},
	"include": [
		"src/**/*",
		"../../src/cypher-ide-dts/cypher-ide.d.ts",
		"../../src/cypher-ide-dts/cypher-ide.proposed.codeActionAI.d.ts",
		"../../src/cypher-ide-dts/cypher-ide.proposed.codeActionRanges.d.ts",
		"../../src/cypher-ide-dts/cypher-ide.proposed.multiDocumentHighlightProvider.d.ts",
		"../../src/cypher-ide-dts/cypher-ide.proposed.workspaceTrust.d.ts",
		"../../src/cypher-ide-dts/cypher-ide.proposed.editorHoverVerbosityLevel.d.ts",
	]
}
