/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as fs from 'fs';
import * as path from 'path';
import * as cypher-ide from 'cypher-ide';
import { memoize } from '../utils/memoize';
import { ILogDirectoryProvider } from './logDirectoryProvider';

export class NodeLogDirectoryProvider implements ILogDirectoryProvider {
	public constructor(
		private readonly context: cypher-ide.ExtensionContext
	) { }

	public getNewLogDirectory(): cypher-ide.Uri | undefined {
		const root = this.logDirectory();
		if (root) {
			try {
				return cypher-ide.Uri.file(fs.mkdtempSync(path.join(root, `tsserver-log-`)));
			} catch (e) {
				return undefined;
			}
		}
		return undefined;
	}

	@memoize
	private logDirectory(): string | undefined {
		try {
			const path = this.context.logPath;
			if (!fs.existsSync(path)) {
				fs.mkdirSync(path);
			}
			return this.context.logPath;
		} catch {
			return undefined;
		}
	}
}
