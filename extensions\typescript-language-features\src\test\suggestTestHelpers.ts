/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import 'mocha';
import * as cypher-ide from 'cypher-ide';
import { onChangedDocument, retryUntilDocumentChanges, wait } from './testUtils';

export async function acceptFirstSuggestion(uri: cypher-ide.Uri, _disposables: cypher-ide.Disposable[]) {
	return retryUntilDocumentChanges(uri, { retries: 10, timeout: 0 }, _disposables, async () => {
		await cypher-ide.commands.executeCommand('editor.action.triggerSuggest');
		await wait(1000);
		await cypher-ide.commands.executeCommand('acceptSelectedSuggestion');
	});
}

export async function typeCommitCharacter(uri: cypher-ide.Uri, character: string, _disposables: cypher-ide.Disposable[]) {
	const didChangeDocument = onChangedDocument(uri, _disposables);
	await cypher-ide.commands.executeCommand('editor.action.triggerSuggest');
	await wait(3000); // Give time for suggestions to show
	await cypher-ide.commands.executeCommand('type', { text: character });
	return await didChangeDocument;
}
