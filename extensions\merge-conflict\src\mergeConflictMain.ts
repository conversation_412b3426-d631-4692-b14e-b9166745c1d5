/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cypher-ide from 'cypher-ide';
import MergeConflictServices from './services';

export function activate(context: cypher-ide.ExtensionContext) {
	// Register disposables
	const services = new MergeConflictServices(context);
	services.begin();
	context.subscriptions.push(services);
}

export function deactivate() {
}

