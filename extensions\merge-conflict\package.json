{"name": "merge-conflict", "publisher": "cypher-ide", "displayName": "%displayName%", "description": "%description%", "icon": "media/icon.png", "version": "1.0.0", "license": "MIT", "aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "engines": {"cypher-ide": "^1.5.0"}, "categories": ["Other"], "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": true}}, "activationEvents": ["onStartupFinished"], "main": "./out/mergeConflictMain", "browser": "./dist/browser/mergeConflictMain", "scripts": {"compile": "gulp compile-extension:merge-conflict", "watch": "gulp watch-extension:merge-conflict"}, "contributes": {"commands": [{"category": "%command.category%", "title": "%command.accept.all-current%", "original": "Accept All Current", "command": "merge-conflict.accept.all-current", "enablement": "!isMergeEditor"}, {"category": "%command.category%", "title": "%command.accept.all-incoming%", "original": "Accept All Incoming", "command": "merge-conflict.accept.all-incoming", "enablement": "!isMergeEditor"}, {"category": "%command.category%", "title": "%command.accept.all-both%", "original": "Accept All Both", "command": "merge-conflict.accept.all-both", "enablement": "!isMergeEditor"}, {"category": "%command.category%", "title": "%command.accept.current%", "original": "Accept Current", "command": "merge-conflict.accept.current", "enablement": "!isMergeEditor"}, {"category": "%command.category%", "title": "%command.accept.incoming%", "original": "Accept Incoming", "command": "merge-conflict.accept.incoming", "enablement": "!isMergeEditor"}, {"category": "%command.category%", "title": "%command.accept.selection%", "original": "Accept Selection", "command": "merge-conflict.accept.selection", "enablement": "!isMergeEditor"}, {"category": "%command.category%", "title": "%command.accept.both%", "original": "Accept Both", "command": "merge-conflict.accept.both", "enablement": "!isMergeEditor"}, {"category": "%command.category%", "title": "%command.next%", "original": "Next Conflict", "command": "merge-conflict.next", "enablement": "!isMergeEditor", "icon": "$(arrow-down)"}, {"category": "%command.category%", "title": "%command.previous%", "original": "Previous Conflict", "command": "merge-conflict.previous", "enablement": "!isMergeEditor", "icon": "$(arrow-up)"}, {"category": "%command.category%", "title": "%command.compare%", "original": "Compare Current Conflict", "command": "merge-conflict.compare", "enablement": "!isMergeEditor"}], "menus": {"scm/resourceState/context": [{"command": "merge-conflict.accept.all-current", "when": "scmProvider == git && scmResourceGroup == merge", "group": "1_modification"}, {"command": "merge-conflict.accept.all-incoming", "when": "scmProvider == git && scmResourceGroup == merge", "group": "1_modification"}], "editor/title": [{"command": "merge-conflict.previous", "group": "navigation@1", "when": "!isMergeEditor && mergeConflictsCount && mergeConflictsCount != 0"}, {"command": "merge-conflict.next", "group": "navigation@2", "when": "!isMergeEditor && mergeConflictsCount && mergeConflictsCount != 0"}]}, "configuration": {"title": "%config.title%", "properties": {"merge-conflict.codeLens.enabled": {"type": "boolean", "description": "%config.codeLensEnabled%", "default": true}, "merge-conflict.decorators.enabled": {"type": "boolean", "description": "%config.decoratorsEnabled%", "default": true}, "merge-conflict.autoNavigateNextConflict.enabled": {"type": "boolean", "description": "%config.autoNavigateNextConflictEnabled%", "default": false}, "merge-conflict.diffViewPosition": {"type": "string", "enum": ["Current", "Beside", "Below"], "description": "%config.diffViewPosition%", "enumDescriptions": ["%config.diffViewPosition.current%", "%config.diffViewPosition.beside%", "%config.diffViewPosition.below%"], "default": "Current"}}}}, "dependencies": {"@cypher-ide/extension-telemetry": "^0.9.8"}, "devDependencies": {"@types/node": "20.x"}, "repository": {"type": "git", "url": "https://github.com/microsoft/cypher-ide.git"}}