// Case 1
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 9,
  "text": "a \"\\${b}\"",
  "innerText": "a \"\\${b}\"",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "command",
      "endIndex": 9,
      "text": "a \"\\${b}\"",
      "innerText": "a \"\\${b}\"",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "word",
          "endIndex": 1,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": []
        },
        {
          "startIndex": 2,
          "type": "string",
          "endIndex": 9,
          "text": "\"\\${b}\"",
          "innerText": "${b}",
          "complete": true,
          "children": []
        }
      ]
    }
  ]
}

// Case 2
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 7,
  "text": "a \"'b'\"",
  "innerText": "a \"'b'\"",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "command",
      "endIndex": 7,
      "text": "a \"'b'\"",
      "innerText": "a \"'b'\"",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "word",
          "endIndex": 1,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": []
        },
        {
          "startIndex": 2,
          "type": "string",
          "endIndex": 7,
          "text": "\"'b'\"",
          "innerText": "'b'",
          "complete": true,
          "children": []
        }
      ]
    }
  ]
}

// Case 3
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 14,
  "text": "a \"\\${b:+\"c\"}\"",
  "innerText": "a \"\\${b:+\"c\"}\"",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "command",
      "endIndex": 14,
      "text": "a \"\\${b:+\"c\"}\"",
      "innerText": "a \"\\${b:+\"c\"}\"",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "word",
          "endIndex": 1,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": []
        },
        {
          "startIndex": 2,
          "type": "concatenation",
          "endIndex": 14,
          "text": "\"\\${b:+\"c\"}\"",
          "innerText": "${b:+c}",
          "complete": true,
          "children": [
            {
              "startIndex": 2,
              "type": "string",
              "endIndex": 10,
              "text": "\"\\${b:+\"",
              "innerText": "${b:+",
              "complete": true,
              "children": []
            },
            {
              "startIndex": 10,
              "type": "word",
              "endIndex": 11,
              "text": "c",
              "innerText": "c",
              "complete": true,
              "children": []
            },
            {
              "startIndex": 11,
              "type": "string",
              "endIndex": 14,
              "text": "\"}\"",
              "innerText": "}",
              "complete": true,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}

// Case 4
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 6,
  "text": "a b\"c\"",
  "innerText": "a b\"c\"",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "command",
      "endIndex": 6,
      "text": "a b\"c\"",
      "innerText": "a b\"c\"",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "word",
          "endIndex": 1,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": []
        },
        {
          "startIndex": 2,
          "type": "concatenation",
          "endIndex": 6,
          "text": "b\"c\"",
          "innerText": "bc",
          "complete": true,
          "children": [
            {
              "startIndex": 2,
              "type": "word",
              "endIndex": 3,
              "text": "b",
              "innerText": "b",
              "complete": true,
              "children": []
            },
            {
              "startIndex": 3,
              "type": "string",
              "endIndex": 6,
              "text": "\"c\"",
              "innerText": "c",
              "complete": true,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}

// Case 5
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 9,
  "text": "a '\\${b}'",
  "innerText": "a '\\${b}'",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "command",
      "endIndex": 9,
      "text": "a '\\${b}'",
      "innerText": "a '\\${b}'",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "word",
          "endIndex": 1,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": []
        },
        {
          "startIndex": 2,
          "type": "raw_string",
          "endIndex": 9,
          "text": "'\\${b}'",
          "innerText": "\\${b}",
          "complete": true,
          "children": []
        }
      ]
    }
  ]
}

// Case 6
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 10,
  "text": "a $'\\${b}'",
  "innerText": "a $'\\${b}'",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "command",
      "endIndex": 10,
      "text": "a $'\\${b}'",
      "innerText": "a $'\\${b}'",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "word",
          "endIndex": 1,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": []
        },
        {
          "startIndex": 2,
          "type": "ansi_c_string",
          "endIndex": 10,
          "text": "$'\\${b}'",
          "innerText": "\\${b}",
          "complete": true,
          "children": []
        }
      ]
    }
  ]
}

// Case 7
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 22,
  "text": "a $'b''c'd$$$e\\${f}\"g\"",
  "innerText": "a $'b''c'd$$$e\\${f}\"g\"",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "command",
      "endIndex": 22,
      "text": "a $'b''c'd$$$e\\${f}\"g\"",
      "innerText": "a $'b''c'd$$$e\\${f}\"g\"",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "word",
          "endIndex": 1,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": []
        },
        {
          "startIndex": 2,
          "type": "concatenation",
          "endIndex": 22,
          "text": "$'b''c'd$$$e\\${f}\"g\"",
          "innerText": "bcd$$$e${f}g",
          "complete": true,
          "children": [
            {
              "startIndex": 2,
              "type": "ansi_c_string",
              "endIndex": 6,
              "text": "$'b'",
              "innerText": "b",
              "complete": true,
              "children": []
            },
            {
              "startIndex": 6,
              "type": "raw_string",
              "endIndex": 9,
              "text": "'c'",
              "innerText": "c",
              "complete": true,
              "children": []
            },
            {
              "startIndex": 9,
              "type": "word",
              "endIndex": 10,
              "text": "d",
              "innerText": "d",
              "complete": true,
              "children": []
            },
            {
              "startIndex": 10,
              "type": "special_expansion",
              "endIndex": 12,
              "text": "$$",
              "innerText": "$$",
              "complete": true,
              "children": []
            },
            {
              "startIndex": 12,
              "type": "simple_expansion",
              "endIndex": 14,
              "text": "$e",
              "innerText": "$e",
              "complete": true,
              "children": []
            },
            {
              "startIndex": 15,
              "type": "word",
              "endIndex": 19,
              "text": "${f}",
              "innerText": "${f}",
              "complete": true,
              "children": []
            },
            {
              "startIndex": 19,
              "type": "string",
              "endIndex": 22,
              "text": "\"g\"",
              "innerText": "g",
              "complete": true,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}

// Case 8
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 10,
  "text": "a $'b\\\\'c'",
  "innerText": "a $'b\\\\'c'",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "command",
      "endIndex": 10,
      "text": "a $'b\\\\'c'",
      "innerText": "a $'b\\\\'c'",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "word",
          "endIndex": 1,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": []
        },
        {
          "startIndex": 2,
          "type": "concatenation",
          "endIndex": 10,
          "text": "$'b\\\\'c'",
          "innerText": "b\\\\c",
          "complete": false,
          "children": [
            {
              "startIndex": 2,
              "type": "ansi_c_string",
              "endIndex": 8,
              "text": "$'b\\\\'",
              "innerText": "b\\\\",
              "complete": true,
              "children": []
            },
            {
              "startIndex": 8,
              "type": "word",
              "endIndex": 9,
              "text": "c",
              "innerText": "c",
              "complete": true,
              "children": []
            },
            {
              "startIndex": 9,
              "type": "raw_string",
              "endIndex": 10,
              "text": "'",
              "innerText": "",
              "complete": false,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}

// Case 9
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 9,
  "text": "a 'b\\\\'c'",
  "innerText": "a 'b\\\\'c'",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "command",
      "endIndex": 9,
      "text": "a 'b\\\\'c'",
      "innerText": "a 'b\\\\'c'",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "word",
          "endIndex": 1,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": []
        },
        {
          "startIndex": 2,
          "type": "concatenation",
          "endIndex": 9,
          "text": "'b\\\\'c'",
          "innerText": "b\\\\c",
          "complete": false,
          "children": [
            {
              "startIndex": 2,
              "type": "raw_string",
              "endIndex": 7,
              "text": "'b\\\\'",
              "innerText": "b\\\\",
              "complete": true,
              "children": []
            },
            {
              "startIndex": 7,
              "type": "word",
              "endIndex": 8,
              "text": "c",
              "innerText": "c",
              "complete": true,
              "children": []
            },
            {
              "startIndex": 8,
              "type": "raw_string",
              "endIndex": 9,
              "text": "'",
              "innerText": "",
              "complete": false,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}

// Case 10
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 6,
  "text": "a \"b$\"",
  "innerText": "a \"b$\"",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "command",
      "endIndex": 6,
      "text": "a \"b$\"",
      "innerText": "a \"b$\"",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "word",
          "endIndex": 1,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": []
        },
        {
          "startIndex": 2,
          "type": "string",
          "endIndex": 6,
          "text": "\"b$\"",
          "innerText": "b$",
          "complete": true,
          "children": []
        }
      ]
    }
  ]
}

// Case 11
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 6,
  "text": "a \"$b\"",
  "innerText": "a \"$b\"",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "command",
      "endIndex": 6,
      "text": "a \"$b\"",
      "innerText": "a \"$b\"",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "word",
          "endIndex": 1,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": []
        },
        {
          "startIndex": 2,
          "type": "string",
          "endIndex": 6,
          "text": "\"$b\"",
          "innerText": "$b",
          "complete": true,
          "children": [
            {
              "startIndex": 3,
              "type": "simple_expansion",
              "endIndex": 5,
              "text": "$b",
              "innerText": "$b",
              "complete": true,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}

// Case 12
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 17,
  "text": "a \"$(b \"c\" && d)\"",
  "innerText": "a \"$(b \"c\" && d)\"",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "command",
      "endIndex": 17,
      "text": "a \"$(b \"c\" && d)\"",
      "innerText": "a \"$(b \"c\" && d)\"",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "word",
          "endIndex": 1,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": []
        },
        {
          "startIndex": 2,
          "type": "string",
          "endIndex": 17,
          "text": "\"$(b \"c\" && d)\"",
          "innerText": "$(b \"c\" && d)",
          "complete": true,
          "children": [
            {
              "startIndex": 3,
              "type": "command_substitution",
              "endIndex": 16,
              "text": "$(b \"c\" && d)",
              "innerText": "$(b \"c\" && d)",
              "complete": true,
              "children": [
                {
                  "startIndex": 5,
                  "type": "list",
                  "endIndex": 15,
                  "text": "b \"c\" && d",
                  "innerText": "b \"c\" && d",
                  "complete": true,
                  "children": [
                    {
                      "startIndex": 5,
                      "type": "command",
                      "endIndex": 11,
                      "text": "b \"c\" ",
                      "innerText": "b \"c\" ",
                      "complete": true,
                      "children": [
                        {
                          "startIndex": 5,
                          "type": "word",
                          "endIndex": 6,
                          "text": "b",
                          "innerText": "b",
                          "complete": true,
                          "children": []
                        },
                        {
                          "startIndex": 7,
                          "type": "string",
                          "endIndex": 10,
                          "text": "\"c\"",
                          "innerText": "c",
                          "complete": true,
                          "children": []
                        }
                      ]
                    },
                    {
                      "startIndex": 14,
                      "type": "command",
                      "endIndex": 15,
                      "text": "d",
                      "innerText": "d",
                      "complete": true,
                      "children": [
                        {
                          "startIndex": 14,
                          "type": "word",
                          "endIndex": 15,
                          "text": "d",
                          "innerText": "d",
                          "complete": true,
                          "children": []
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}