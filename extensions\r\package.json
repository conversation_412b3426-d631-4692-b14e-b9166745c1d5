{"name": "r", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "cypher-ide", "license": "MIT", "engines": {"cypher-ide": "*"}, "scripts": {"update-grammar": "node ../node_modules/cypher-ide-grammar-updater/bin REditorSupport/cypher-ide-R syntax/r.json ./syntaxes/r.tmLanguage.json"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "r", "extensions": [".r", ".rhistory", ".rprofile", ".rt"], "aliases": ["R", "r"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "r", "scopeName": "source.r", "path": "./syntaxes/r.tmLanguage.json"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/cypher-ide.git"}}