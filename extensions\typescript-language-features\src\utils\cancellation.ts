/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cypher-ide from 'cypher-ide';

const noopDisposable = cypher-ide.Disposable.from();

export const nulToken: cypher-ide.CancellationToken = {
	isCancellationRequested: false,
	onCancellationRequested: () => noopDisposable
};
