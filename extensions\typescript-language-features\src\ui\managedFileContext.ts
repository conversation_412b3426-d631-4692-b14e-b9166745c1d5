/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cypher-ide from 'cypher-ide';
import { disabledSchemes } from '../configuration/fileSchemes';
import { isJsConfigOrTsConfigFileName } from '../configuration/languageDescription';
import { isSupportedLanguageMode } from '../configuration/languageIds';
import { Disposable } from '../utils/dispose';
import { ActiveJsTsEditorTracker } from './activeJsTsEditorTracker';

/**
 * When clause context set when the current file is managed by cypher-ide's built-in typescript extension.
 */
export default class ManagedFileContextManager extends Disposable {
	private static readonly contextName = 'typescript.isManagedFile';

	private isInManagedFileContext: boolean = false;

	public constructor(activeJsTsEditorTracker: ActiveJsTsEditorTracker) {
		super();
		activeJsTsEditorTracker.onDidChangeActiveJsTsEditor(this.onDidChangeActiveTextEditor, this, this._disposables);

		this.onDidChangeActiveTextEditor(activeJsTsEditorTracker.activeJsTsEditor);
	}

	private onDidChangeActiveTextEditor(editor?: cypher-ide.TextEditor): void {
		if (editor) {
			this.updateContext(this.isManagedFile(editor));
		} else {
			this.updateContext(false);
		}
	}

	private updateContext(newValue: boolean) {
		if (newValue === this.isInManagedFileContext) {
			return;
		}

		cypher-ide.commands.executeCommand('setContext', ManagedFileContextManager.contextName, newValue);
		this.isInManagedFileContext = newValue;
	}

	private isManagedFile(editor: cypher-ide.TextEditor): boolean {
		return this.isManagedScriptFile(editor) || this.isManagedConfigFile(editor);
	}

	private isManagedScriptFile(editor: cypher-ide.TextEditor): boolean {
		return isSupportedLanguageMode(editor.document) && !disabledSchemes.has(editor.document.uri.scheme);
	}

	private isManagedConfigFile(editor: cypher-ide.TextEditor): boolean {
		return isJsConfigOrTsConfigFileName(editor.document.fileName);
	}
}

