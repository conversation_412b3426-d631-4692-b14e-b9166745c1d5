/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cypher-ide from 'cypher-ide';
import { CachedResponse } from '../../tsServer/cachedResponse';
import type * as Proto from '../../tsServer/protocol/protocol';
import * as typeConverters from '../../typeConverters';
import { ITypeScriptServiceClient } from '../../typescriptService';
import { escapeRegExp } from '../../utils/regexp';
import { Disposable } from '../../utils/dispose';


export class ReferencesCodeLens extends cypher-ide.CodeLens {
	constructor(
		public document: cypher-ide.Uri,
		public file: string,
		range: cypher-ide.Range
	) {
		super(range);
	}
}

export abstract class TypeScriptBaseCodeLensProvider extends Disposable implements cypher-ide.CodeLensProvider<ReferencesCodeLens> {
	protected changeEmitter = this._register(new cypher-ide.EventEmitter<void>());
	public onDidChangeCodeLenses = this.changeEmitter.event;

	public static readonly cancelledCommand: cypher-ide.Command = {
		// Cancellation is not an error. Just show nothing until we can properly re-compute the code lens
		title: '',
		command: ''
	};

	public static readonly errorCommand: cypher-ide.Command = {
		title: cypher-ide.l10n.t("Could not determine references"),
		command: ''
	};

	public constructor(
		protected client: ITypeScriptServiceClient,
		private readonly cachedResponse: CachedResponse<Proto.NavTreeResponse>
	) {
		super();
	}

	async provideCodeLenses(document: cypher-ide.TextDocument, token: cypher-ide.CancellationToken): Promise<ReferencesCodeLens[]> {
		const filepath = this.client.toOpenTsFilePath(document);
		if (!filepath) {
			return [];
		}

		const response = await this.cachedResponse.execute(document, () => this.client.execute('navtree', { file: filepath }, token));
		if (response.type !== 'response') {
			return [];
		}

		const referenceableSpans: cypher-ide.Range[] = [];
		response.body?.childItems?.forEach(item => this.walkNavTree(document, item, undefined, referenceableSpans));
		return referenceableSpans.map(span => new ReferencesCodeLens(document.uri, filepath, span));
	}

	protected abstract extractSymbol(
		document: cypher-ide.TextDocument,
		item: Proto.NavigationTree,
		parent: Proto.NavigationTree | undefined
	): cypher-ide.Range | undefined;

	private walkNavTree(
		document: cypher-ide.TextDocument,
		item: Proto.NavigationTree,
		parent: Proto.NavigationTree | undefined,
		results: cypher-ide.Range[]
	): void {
		const range = this.extractSymbol(document, item, parent);
		if (range) {
			results.push(range);
		}

		item.childItems?.forEach(child => this.walkNavTree(document, child, item, results));
	}
}

export function getSymbolRange(
	document: cypher-ide.TextDocument,
	item: Proto.NavigationTree
): cypher-ide.Range | undefined {
	if (item.nameSpan) {
		return typeConverters.Range.fromTextSpan(item.nameSpan);
	}

	// In older versions, we have to calculate this manually. See #23924
	const span = item.spans?.[0];
	if (!span) {
		return undefined;
	}

	const range = typeConverters.Range.fromTextSpan(span);
	const text = document.getText(range);

	const identifierMatch = new RegExp(`^(.*?(\\b|\\W))${escapeRegExp(item.text || '')}(\\b|\\W)`, 'gm');
	const match = identifierMatch.exec(text);
	const prefixLength = match ? match.index + match[1].length : 0;
	const startOffset = document.offsetAt(new cypher-ide.Position(range.start.line, range.start.character)) + prefixLength;
	return new cypher-ide.Range(
		document.positionAt(startOffset),
		document.positionAt(startOffset + item.text.length));
}
