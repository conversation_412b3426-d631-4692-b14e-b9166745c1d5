/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cypher-ide from 'cypher-ide';
import * as interfaces from './interfaces';

export default class MergeConflictCodeLensProvider implements cypher-ide.CodeLensProvider, cypher-ide.Disposable {
	private codeLensRegistrationHandle?: cypher-ide.Disposable | null;
	private config?: interfaces.IExtensionConfiguration;
	private tracker: interfaces.IDocumentMergeConflictTracker;

	constructor(trackerService: interfaces.IDocumentMergeConflictTrackerService) {
		this.tracker = trackerService.createTracker('codelens');
	}

	begin(config: interfaces.IExtensionConfiguration) {
		this.config = config;

		if (this.config.enableCodeLens) {
			this.registerCodeLensProvider();
		}
	}

	configurationUpdated(updatedConfig: interfaces.IExtensionConfiguration) {

		if (updatedConfig.enableCodeLens === false && this.codeLensRegistrationHandle) {
			this.codeLensRegistrationHandle.dispose();
			this.codeLensRegistrationHandle = null;
		}
		else if (updatedConfig.enableCodeLens === true && !this.codeLensRegistrationHandle) {
			this.registerCodeLensProvider();
		}

		this.config = updatedConfig;
	}


	dispose() {
		if (this.codeLensRegistrationHandle) {
			this.codeLensRegistrationHandle.dispose();
			this.codeLensRegistrationHandle = null;
		}
	}

	async provideCodeLenses(document: cypher-ide.TextDocument, _token: cypher-ide.CancellationToken): Promise<cypher-ide.CodeLens[] | null> {

		if (!this.config || !this.config.enableCodeLens) {
			return null;
		}

		const conflicts = await this.tracker.getConflicts(document);
		const conflictsCount = conflicts?.length ?? 0;
		cypher-ide.commands.executeCommand('setContext', 'mergeConflictsCount', conflictsCount);

		if (!conflictsCount) {
			return null;
		}

		const items: cypher-ide.CodeLens[] = [];

		conflicts.forEach(conflict => {
			const acceptCurrentCommand: cypher-ide.Command = {
				command: 'merge-conflict.accept.current',
				title: cypher-ide.l10n.t("Accept Current Change"),
				arguments: ['known-conflict', conflict]
			};

			const acceptIncomingCommand: cypher-ide.Command = {
				command: 'merge-conflict.accept.incoming',
				title: cypher-ide.l10n.t("Accept Incoming Change"),
				arguments: ['known-conflict', conflict]
			};

			const acceptBothCommand: cypher-ide.Command = {
				command: 'merge-conflict.accept.both',
				title: cypher-ide.l10n.t("Accept Both Changes"),
				arguments: ['known-conflict', conflict]
			};

			const diffCommand: cypher-ide.Command = {
				command: 'merge-conflict.compare',
				title: cypher-ide.l10n.t("Compare Changes"),
				arguments: [conflict]
			};

			const range = document.lineAt(conflict.range.start.line).range;
			items.push(
				new cypher-ide.CodeLens(range, acceptCurrentCommand),
				new cypher-ide.CodeLens(range, acceptIncomingCommand),
				new cypher-ide.CodeLens(range, acceptBothCommand),
				new cypher-ide.CodeLens(range, diffCommand)
			);
		});

		return items;
	}

	private registerCodeLensProvider() {
		this.codeLensRegistrationHandle = cypher-ide.languages.registerCodeLensProvider([
			{ scheme: 'file' },
			{ scheme: 'cypher-ide-vfs' },
			{ scheme: 'untitled' },
			{ scheme: 'cypher-ide-userdata' },
		], this);
	}
}
