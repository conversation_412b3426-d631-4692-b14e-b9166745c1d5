{
	"extends": "../tsconfig.base.json",
	"compilerOptions": {
		"outDir": "./out",
		"esModuleInterop": true,
		"experimentalDecorators": true,
		"types": [
			"node"
		],

		// Needed to suppress warnings in upstream completions
		"noImplicitReturns": false,
		"noUnusedParameters": false
	},
	"include": [
		"src/**/*",
		"src/completions/index.d.ts",
		"../../src/cypher-ide-dts/cypher-ide.d.ts",
		"../../src/cypher-ide-dts/cypher-ide.proposed.terminalCompletionProvider.d.ts",
		"../../src/cypher-ide-dts/cypher-ide.proposed.terminalShellEnv.d.ts"
	]
}
