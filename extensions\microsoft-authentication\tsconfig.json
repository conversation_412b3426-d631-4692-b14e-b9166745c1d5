{"extends": "../tsconfig.base.json", "compilerOptions": {"baseUrl": ".", "experimentalDecorators": true, "module": "commonjs", "moduleResolution": "node", "noFallthroughCasesInSwitch": true, "noUnusedLocals": false, "outDir": "dist", "resolveJsonModule": true, "rootDir": "src", "skipLibCheck": true, "sourceMap": true, "lib": ["WebWorker"]}, "exclude": ["node_modules"], "include": ["src/**/*", "../../src/cypher-ide-dts/cypher-ide.d.ts", "../../src/cypher-ide-dts/cypher-ide.proposed.idToken.d.ts", "../../src/cypher-ide-dts/cypher-ide.proposed.nativeWindowHandle.d.ts"]}