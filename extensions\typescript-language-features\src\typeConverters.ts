/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

/**
 * Helpers for converting FROM cypher-ide types TO ts types
 */

import * as cypher-ide from 'cypher-ide';
import type * as Proto from './tsServer/protocol/protocol';
import * as PConst from './tsServer/protocol/protocol.const';
import { ITypeScriptServiceClient } from './typescriptService';

export namespace Range {
	export const fromTextSpan = (span: Proto.TextSpan): cypher-ide.Range =>
		fromLocations(span.start, span.end);

	export const toTextSpan = (range: cypher-ide.Range): Proto.TextSpan => ({
		start: Position.toLocation(range.start),
		end: Position.toLocation(range.end)
	});

	export const fromLocations = (start: Proto.Location, end: Proto.Location): cypher-ide.Range =>
		new cypher-ide.Range(
			Math.max(0, start.line - 1), Math.max(start.offset - 1, 0),
			Math.max(0, end.line - 1), Math.max(0, end.offset - 1));

	export const toFileRange = (range: cypher-ide.Range): Proto.FileRange => ({
		startLine: range.start.line + 1,
		startOffset: range.start.character + 1,
		endLine: range.end.line + 1,
		endOffset: range.end.character + 1
	});

	export const toFileRangeRequestArgs = (file: string, range: cypher-ide.Range): Proto.FileRangeRequestArgs => ({
		file,
		...toFileRange(range)
	});

	export const toFileRangesRequestArgs = (file: string, ranges: cypher-ide.Range[]): Proto.FileRangesRequestArgs => ({
		file,
		ranges: ranges.map(toFileRange)
	});

	export const toFormattingRequestArgs = (file: string, range: cypher-ide.Range): Proto.FormatRequestArgs => ({
		file,
		line: range.start.line + 1,
		offset: range.start.character + 1,
		endLine: range.end.line + 1,
		endOffset: range.end.character + 1
	});
}

export namespace Position {
	export const fromLocation = (tslocation: Proto.Location): cypher-ide.Position =>
		new cypher-ide.Position(tslocation.line - 1, tslocation.offset - 1);

	export const toLocation = (vsPosition: cypher-ide.Position): Proto.Location => ({
		line: vsPosition.line + 1,
		offset: vsPosition.character + 1,
	});

	export const toFileLocationRequestArgs = (file: string, position: cypher-ide.Position): Proto.FileLocationRequestArgs => ({
		file,
		line: position.line + 1,
		offset: position.character + 1,
	});
}

export namespace Location {
	export const fromTextSpan = (resource: cypher-ide.Uri, tsTextSpan: Proto.TextSpan): cypher-ide.Location =>
		new cypher-ide.Location(resource, Range.fromTextSpan(tsTextSpan));
}

export namespace TextEdit {
	export const fromCodeEdit = (edit: Proto.CodeEdit): cypher-ide.TextEdit =>
		new cypher-ide.TextEdit(
			Range.fromTextSpan(edit),
			edit.newText);
}

export namespace WorkspaceEdit {
	export function fromFileCodeEdits(
		client: ITypeScriptServiceClient,
		edits: Iterable<Proto.FileCodeEdits>
	): cypher-ide.WorkspaceEdit {
		return withFileCodeEdits(new cypher-ide.WorkspaceEdit(), client, edits);
	}

	export function withFileCodeEdits(
		workspaceEdit: cypher-ide.WorkspaceEdit,
		client: ITypeScriptServiceClient,
		edits: Iterable<Proto.FileCodeEdits>
	): cypher-ide.WorkspaceEdit {
		for (const edit of edits) {
			const resource = client.toResource(edit.fileName);
			for (const textChange of edit.textChanges) {
				workspaceEdit.replace(resource,
					Range.fromTextSpan(textChange),
					textChange.newText);
			}
		}

		return workspaceEdit;
	}
}

export namespace SymbolKind {
	export function fromProtocolScriptElementKind(kind: Proto.ScriptElementKind) {
		switch (kind) {
			case PConst.Kind.module: return cypher-ide.SymbolKind.Module;
			case PConst.Kind.class: return cypher-ide.SymbolKind.Class;
			case PConst.Kind.enum: return cypher-ide.SymbolKind.Enum;
			case PConst.Kind.enumMember: return cypher-ide.SymbolKind.EnumMember;
			case PConst.Kind.interface: return cypher-ide.SymbolKind.Interface;
			case PConst.Kind.indexSignature: return cypher-ide.SymbolKind.Method;
			case PConst.Kind.callSignature: return cypher-ide.SymbolKind.Method;
			case PConst.Kind.method: return cypher-ide.SymbolKind.Method;
			case PConst.Kind.memberVariable: return cypher-ide.SymbolKind.Property;
			case PConst.Kind.memberGetAccessor: return cypher-ide.SymbolKind.Property;
			case PConst.Kind.memberSetAccessor: return cypher-ide.SymbolKind.Property;
			case PConst.Kind.variable: return cypher-ide.SymbolKind.Variable;
			case PConst.Kind.let: return cypher-ide.SymbolKind.Variable;
			case PConst.Kind.const: return cypher-ide.SymbolKind.Variable;
			case PConst.Kind.localVariable: return cypher-ide.SymbolKind.Variable;
			case PConst.Kind.alias: return cypher-ide.SymbolKind.Variable;
			case PConst.Kind.function: return cypher-ide.SymbolKind.Function;
			case PConst.Kind.localFunction: return cypher-ide.SymbolKind.Function;
			case PConst.Kind.constructSignature: return cypher-ide.SymbolKind.Constructor;
			case PConst.Kind.constructorImplementation: return cypher-ide.SymbolKind.Constructor;
			case PConst.Kind.typeParameter: return cypher-ide.SymbolKind.TypeParameter;
			case PConst.Kind.string: return cypher-ide.SymbolKind.String;
			default: return cypher-ide.SymbolKind.Variable;
		}
	}
}

export namespace CompletionTriggerKind {
	export function toProtocolCompletionTriggerKind(kind: cypher-ide.CompletionTriggerKind): Proto.CompletionTriggerKind {
		switch (kind) {
			case cypher-ide.CompletionTriggerKind.Invoke: return 1;
			case cypher-ide.CompletionTriggerKind.TriggerCharacter: return 2;
			case cypher-ide.CompletionTriggerKind.TriggerForIncompleteCompletions: return 3;
		}
	}
}

export namespace OrganizeImportsMode {
	export function toProtocolOrganizeImportsMode(mode: PConst.OrganizeImportsMode): Proto.OrganizeImportsMode {
		switch (mode) {
			case PConst.OrganizeImportsMode.All: return 'All' as Proto.OrganizeImportsMode.All;
			case PConst.OrganizeImportsMode.SortAndCombine: return 'SortAndCombine' as Proto.OrganizeImportsMode.SortAndCombine;
			case PConst.OrganizeImportsMode.RemoveUnused: return 'RemoveUnused' as Proto.OrganizeImportsMode.RemoveUnused;
		}
	}
}
