/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as assert from 'assert';
import 'mocha';
import * as cypher-ide from 'cypher-ide';
import { createTestEditor, joinLines, wait } from '../../test/testUtils';
import { disposeAll } from '../../utils/dispose';

const testDocumentUri = cypher-ide.Uri.parse('untitled:test.ts');

const emptyRange = new cypher-ide.Range(new cypher-ide.Position(0, 0), new cypher-ide.Position(0, 0));

suite.skip('TypeScript Fix All', () => {

	const _disposables: cypher-ide.Disposable[] = [];

	setup(async () => {
		// the tests assume that typescript features are registered
		await cypher-ide.extensions.getExtension('cypher-ide.typescript-language-features')!.activate();
	});

	teardown(async () => {
		disposeAll(_disposables);

		await cypher-ide.commands.executeCommand('workbench.action.closeAllEditors');
	});

	test('Fix all should remove unreachable code', async () => {
		const editor = await createTestEditor(testDocumentUri,
			`function foo() {`,
			`    return 1;`,
			`    return 2;`,
			`};`,
			`function boo() {`,
			`    return 3;`,
			`    return 4;`,
			`};`,
		);

		await wait(2000);

		const fixes = await cypher-ide.commands.executeCommand<cypher-ide.CodeAction[]>('cypher-ide.executeCodeActionProvider',
			testDocumentUri,
			emptyRange,
			cypher-ide.CodeActionKind.SourceFixAll
		);

		await cypher-ide.workspace.applyEdit(fixes![0].edit!);

		assert.strictEqual(editor.document.getText(), joinLines(
			`function foo() {`,
			`    return 1;`,
			`};`,
			`function boo() {`,
			`    return 3;`,
			`};`,
		));

	});

	test('Fix all should implement interfaces', async () => {
		const editor = await createTestEditor(testDocumentUri,
			`interface I {`,
			`    x: number;`,
			`}`,
			`class A implements I {}`,
			`class B implements I {}`,
		);

		await wait(2000);

		const fixes = await cypher-ide.commands.executeCommand<cypher-ide.CodeAction[]>('cypher-ide.executeCodeActionProvider',
			testDocumentUri,
			emptyRange,
			cypher-ide.CodeActionKind.SourceFixAll
		);

		await cypher-ide.workspace.applyEdit(fixes![0].edit!);
		assert.strictEqual(editor.document.getText(), joinLines(
			`interface I {`,
			`    x: number;`,
			`}`,
			`class A implements I {`,
			`    x: number;`,
			`}`,
			`class B implements I {`,
			`    x: number;`,
			`}`,
		));
	});

	test('Remove unused should handle nested ununused', async () => {
		const editor = await createTestEditor(testDocumentUri,
			`export const _ = 1;`,
			`function unused() {`,
			`    const a = 1;`,
			`}`,
			`function used() {`,
			`    const a = 1;`,
			`}`,
			`used();`
		);

		await wait(2000);

		const fixes = await cypher-ide.commands.executeCommand<cypher-ide.CodeAction[]>('cypher-ide.executeCodeActionProvider',
			testDocumentUri,
			emptyRange,
			cypher-ide.CodeActionKind.Source.append('removeUnused')
		);

		await cypher-ide.workspace.applyEdit(fixes![0].edit!);
		assert.strictEqual(editor.document.getText(), joinLines(
			`export const _ = 1;`,
			`function used() {`,
			`}`,
			`used();`
		));
	});

	test('Remove unused should remove unused interfaces', async () => {
		const editor = await createTestEditor(testDocumentUri,
			`export const _ = 1;`,
			`interface Foo {}`
		);

		await wait(2000);

		const fixes = await cypher-ide.commands.executeCommand<cypher-ide.CodeAction[]>('cypher-ide.executeCodeActionProvider',
			testDocumentUri,
			emptyRange,
			cypher-ide.CodeActionKind.Source.append('removeUnused')
		);

		await cypher-ide.workspace.applyEdit(fixes![0].edit!);
		assert.strictEqual(editor.document.getText(), joinLines(
			`export const _ = 1;`,
			``
		));
	});
});
