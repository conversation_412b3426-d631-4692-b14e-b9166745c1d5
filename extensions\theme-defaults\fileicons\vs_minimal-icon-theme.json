{
	"iconDefinitions": {
		"_root_folder_dark": {
			"iconPath": "./images/root-folder-dark.svg"
		},
		"_root_folder_open_dark": {
			"iconPath": "./images/root-folder-open-dark.svg"
		},
		"_folder_dark": {
			"iconPath": "./images/folder-dark.svg"
		},
		"_folder_open_dark": {
			"iconPath": "./images/folder-open-dark.svg"
		},
		"_file_dark": {
			"iconPath": "./images/document-dark.svg"
		},
		"_root_folder": {
			"iconPath": "./images/root-folder-light.svg"
		},
		"_root_folder_open": {
			"iconPath": "./images/root-folder-open-light.svg"
		},
		"_folder_light": {
			"iconPath": "./images/folder-light.svg"
		},
		"_folder_open_light": {
			"iconPath": "./images/folder-open-light.svg"
		},
		"_file_light": {
			"iconPath": "./images/document-light.svg"
		}
	},

	"folderExpanded": "_folder_open_dark",
	"folder": "_folder_dark",
	"file": "_file_dark",
	"rootFolderExpanded": "_root_folder_open_dark",
	"rootFolder": "_root_folder_dark",
	"fileExtensions": {
		// icons by file extension
	},
	"fileNames": {
		// icons by file name
	},
	"languageIds": {
		// icons by language id
	},
	"light": {
		"folderExpanded": "_folder_open_light",
		"folder": "_folder_light",
		"rootFolderExpanded": "_root_folder_open",
		"rootFolder": "_root_folder",
		"file": "_file_light",
		"fileExtensions": {
			// icons by file extension
		},
		"fileNames": {
			// icons by file name
		},
		"languageIds": {
			// icons by language id
		}
	},
	"highContrast": {
		// overrides for high contrast
	}
}