// Case 1
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 6,
  "text": "a && b",
  "innerText": "a && b",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "list",
      "endIndex": 6,
      "text": "a && b",
      "innerText": "a && b",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "command",
          "endIndex": 2,
          "text": "a ",
          "innerText": "a ",
          "complete": true,
          "children": [
            {
              "startIndex": 0,
              "type": "word",
              "endIndex": 1,
              "text": "a",
              "innerText": "a",
              "complete": true,
              "children": []
            }
          ]
        },
        {
          "startIndex": 5,
          "type": "command",
          "endIndex": 6,
          "text": "b",
          "innerText": "b",
          "complete": true,
          "children": [
            {
              "startIndex": 5,
              "type": "word",
              "endIndex": 6,
              "text": "b",
              "innerText": "b",
              "complete": true,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}

// Case 2
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 6,
  "text": "a || b",
  "innerText": "a || b",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "list",
      "endIndex": 6,
      "text": "a || b",
      "innerText": "a || b",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "command",
          "endIndex": 2,
          "text": "a ",
          "innerText": "a ",
          "complete": true,
          "children": [
            {
              "startIndex": 0,
              "type": "word",
              "endIndex": 1,
              "text": "a",
              "innerText": "a",
              "complete": true,
              "children": []
            }
          ]
        },
        {
          "startIndex": 5,
          "type": "command",
          "endIndex": 6,
          "text": "b",
          "innerText": "b",
          "complete": true,
          "children": [
            {
              "startIndex": 5,
              "type": "word",
              "endIndex": 6,
              "text": "b",
              "innerText": "b",
              "complete": true,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}

// Case 3
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 5,
  "text": "a | b",
  "innerText": "a | b",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "pipeline",
      "endIndex": 5,
      "text": "a | b",
      "innerText": "a | b",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "command",
          "endIndex": 2,
          "text": "a ",
          "innerText": "a ",
          "complete": true,
          "children": [
            {
              "startIndex": 0,
              "type": "word",
              "endIndex": 1,
              "text": "a",
              "innerText": "a",
              "complete": true,
              "children": []
            }
          ]
        },
        {
          "startIndex": 4,
          "type": "command",
          "endIndex": 5,
          "text": "b",
          "innerText": "b",
          "complete": true,
          "children": [
            {
              "startIndex": 4,
              "type": "word",
              "endIndex": 5,
              "text": "b",
              "innerText": "b",
              "complete": true,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}

// Case 4
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 6,
  "text": "a |& b",
  "innerText": "a |& b",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "pipeline",
      "endIndex": 6,
      "text": "a |& b",
      "innerText": "a |& b",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "command",
          "endIndex": 2,
          "text": "a ",
          "innerText": "a ",
          "complete": true,
          "children": [
            {
              "startIndex": 0,
              "type": "word",
              "endIndex": 1,
              "text": "a",
              "innerText": "a",
              "complete": true,
              "children": []
            }
          ]
        },
        {
          "startIndex": 5,
          "type": "command",
          "endIndex": 6,
          "text": "b",
          "innerText": "b",
          "complete": true,
          "children": [
            {
              "startIndex": 5,
              "type": "word",
              "endIndex": 6,
              "text": "b",
              "innerText": "b",
              "complete": true,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}

// Case 5
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 6,
  "text": "(a; b)",
  "innerText": "(a; b)",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "subshell",
      "endIndex": 6,
      "text": "(a; b)",
      "innerText": "(a; b)",
      "complete": true,
      "children": [
        {
          "startIndex": 1,
          "type": "command",
          "endIndex": 2,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": [
            {
              "startIndex": 1,
              "type": "word",
              "endIndex": 2,
              "text": "a",
              "innerText": "a",
              "complete": true,
              "children": []
            }
          ]
        },
        {
          "startIndex": 4,
          "type": "command",
          "endIndex": 5,
          "text": "b",
          "innerText": "b",
          "complete": true,
          "children": [
            {
              "startIndex": 4,
              "type": "word",
              "endIndex": 5,
              "text": "b",
              "innerText": "b",
              "complete": true,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}

// Case 6
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 7,
  "text": "(a; b;)",
  "innerText": "(a; b;)",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "subshell",
      "endIndex": 7,
      "text": "(a; b;)",
      "innerText": "(a; b;)",
      "complete": true,
      "children": [
        {
          "startIndex": 1,
          "type": "command",
          "endIndex": 2,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": [
            {
              "startIndex": 1,
              "type": "word",
              "endIndex": 2,
              "text": "a",
              "innerText": "a",
              "complete": true,
              "children": []
            }
          ]
        },
        {
          "startIndex": 4,
          "type": "command",
          "endIndex": 5,
          "text": "b",
          "innerText": "b",
          "complete": true,
          "children": [
            {
              "startIndex": 4,
              "type": "word",
              "endIndex": 5,
              "text": "b",
              "innerText": "b",
              "complete": true,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}

// Case 7
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 6,
  "text": "{a; b}",
  "innerText": "{a; b}",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "compound_statement",
      "endIndex": 6,
      "text": "{a; b}",
      "innerText": "{a; b}",
      "complete": false,
      "children": [
        {
          "startIndex": 1,
          "type": "command",
          "endIndex": 2,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": [
            {
              "startIndex": 1,
              "type": "word",
              "endIndex": 2,
              "text": "a",
              "innerText": "a",
              "complete": true,
              "children": []
            }
          ]
        },
        {
          "startIndex": 4,
          "type": "command",
          "endIndex": 6,
          "text": "b}",
          "innerText": "b}",
          "complete": true,
          "children": [
            {
              "startIndex": 4,
              "type": "word",
              "endIndex": 6,
              "text": "b}",
              "innerText": "b}",
              "complete": true,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}

// Case 8
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 7,
  "text": "{a; b;}",
  "innerText": "{a; b;}",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "compound_statement",
      "endIndex": 7,
      "text": "{a; b;}",
      "innerText": "{a; b;}",
      "complete": true,
      "children": [
        {
          "startIndex": 1,
          "type": "command",
          "endIndex": 2,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": [
            {
              "startIndex": 1,
              "type": "word",
              "endIndex": 2,
              "text": "a",
              "innerText": "a",
              "complete": true,
              "children": []
            }
          ]
        },
        {
          "startIndex": 4,
          "type": "command",
          "endIndex": 5,
          "text": "b",
          "innerText": "b",
          "complete": true,
          "children": [
            {
              "startIndex": 4,
              "type": "word",
              "endIndex": 5,
              "text": "b",
              "innerText": "b",
              "complete": true,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}

// Case 9
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 4,
  "text": "a; b",
  "innerText": "a; b",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "command",
      "endIndex": 1,
      "text": "a",
      "innerText": "a",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "word",
          "endIndex": 1,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": []
        }
      ]
    },
    {
      "startIndex": 3,
      "type": "command",
      "endIndex": 4,
      "text": "b",
      "innerText": "b",
      "complete": true,
      "children": [
        {
          "startIndex": 3,
          "type": "word",
          "endIndex": 4,
          "text": "b",
          "innerText": "b",
          "complete": true,
          "children": []
        }
      ]
    }
  ]
}

// Case 10
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 5,
  "text": "a & b",
  "innerText": "a & b",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "command",
      "endIndex": 2,
      "text": "a ",
      "innerText": "a ",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "word",
          "endIndex": 1,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": []
        }
      ]
    },
    {
      "startIndex": 4,
      "type": "command",
      "endIndex": 5,
      "text": "b",
      "innerText": "b",
      "complete": true,
      "children": [
        {
          "startIndex": 4,
          "type": "word",
          "endIndex": 5,
          "text": "b",
          "innerText": "b",
          "complete": true,
          "children": []
        }
      ]
    }
  ]
}

// Case 11
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 6,
  "text": "a &; b",
  "innerText": "a &; b",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "command",
      "endIndex": 2,
      "text": "a ",
      "innerText": "a ",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "word",
          "endIndex": 1,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": []
        }
      ]
    },
    {
      "startIndex": 5,
      "type": "command",
      "endIndex": 6,
      "text": "b",
      "innerText": "b",
      "complete": true,
      "children": [
        {
          "startIndex": 5,
          "type": "word",
          "endIndex": 6,
          "text": "b",
          "innerText": "b",
          "complete": true,
          "children": []
        }
      ]
    }
  ]
}

// Case 12
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 6,
  "text": "a ; b;",
  "innerText": "a ; b;",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "command",
      "endIndex": 2,
      "text": "a ",
      "innerText": "a ",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "word",
          "endIndex": 1,
          "text": "a",
          "innerText": "a",
          "complete": true,
          "children": []
        }
      ]
    },
    {
      "startIndex": 4,
      "type": "command",
      "endIndex": 5,
      "text": "b",
      "innerText": "b",
      "complete": true,
      "children": [
        {
          "startIndex": 4,
          "type": "word",
          "endIndex": 5,
          "text": "b",
          "innerText": "b",
          "complete": true,
          "children": []
        }
      ]
    }
  ]
}

// Case 13
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 11,
  "text": "a && b || c",
  "innerText": "a && b || c",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "list",
      "endIndex": 11,
      "text": "a && b || c",
      "innerText": "a && b || c",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "command",
          "endIndex": 2,
          "text": "a ",
          "innerText": "a ",
          "complete": true,
          "children": [
            {
              "startIndex": 0,
              "type": "word",
              "endIndex": 1,
              "text": "a",
              "innerText": "a",
              "complete": true,
              "children": []
            }
          ]
        },
        {
          "startIndex": 5,
          "type": "command",
          "endIndex": 7,
          "text": "b ",
          "innerText": "b ",
          "complete": true,
          "children": [
            {
              "startIndex": 5,
              "type": "word",
              "endIndex": 6,
              "text": "b",
              "innerText": "b",
              "complete": true,
              "children": []
            }
          ]
        },
        {
          "startIndex": 10,
          "type": "command",
          "endIndex": 11,
          "text": "c",
          "innerText": "c",
          "complete": true,
          "children": [
            {
              "startIndex": 10,
              "type": "word",
              "endIndex": 11,
              "text": "c",
              "innerText": "c",
              "complete": true,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}

// Case 14
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 10,
  "text": "a && b | c",
  "innerText": "a && b | c",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "list",
      "endIndex": 10,
      "text": "a && b | c",
      "innerText": "a && b | c",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "command",
          "endIndex": 2,
          "text": "a ",
          "innerText": "a ",
          "complete": true,
          "children": [
            {
              "startIndex": 0,
              "type": "word",
              "endIndex": 1,
              "text": "a",
              "innerText": "a",
              "complete": true,
              "children": []
            }
          ]
        },
        {
          "startIndex": 5,
          "type": "pipeline",
          "endIndex": 10,
          "text": "b | c",
          "innerText": "b | c",
          "complete": true,
          "children": [
            {
              "startIndex": 5,
              "type": "command",
              "endIndex": 7,
              "text": "b ",
              "innerText": "b ",
              "complete": true,
              "children": [
                {
                  "startIndex": 5,
                  "type": "word",
                  "endIndex": 6,
                  "text": "b",
                  "innerText": "b",
                  "complete": true,
                  "children": []
                }
              ]
            },
            {
              "startIndex": 9,
              "type": "command",
              "endIndex": 10,
              "text": "c",
              "innerText": "c",
              "complete": true,
              "children": [
                {
                  "startIndex": 9,
                  "type": "word",
                  "endIndex": 10,
                  "text": "c",
                  "innerText": "c",
                  "complete": true,
                  "children": []
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}

// Case 15
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 10,
  "text": "a | b && c",
  "innerText": "a | b && c",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "list",
      "endIndex": 10,
      "text": "a | b && c",
      "innerText": "a | b && c",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "pipeline",
          "endIndex": 6,
          "text": "a | b ",
          "innerText": "a | b ",
          "complete": true,
          "children": [
            {
              "startIndex": 0,
              "type": "command",
              "endIndex": 2,
              "text": "a ",
              "innerText": "a ",
              "complete": true,
              "children": [
                {
                  "startIndex": 0,
                  "type": "word",
                  "endIndex": 1,
                  "text": "a",
                  "innerText": "a",
                  "complete": true,
                  "children": []
                }
              ]
            },
            {
              "startIndex": 4,
              "type": "command",
              "endIndex": 6,
              "text": "b ",
              "innerText": "b ",
              "complete": true,
              "children": [
                {
                  "startIndex": 4,
                  "type": "word",
                  "endIndex": 5,
                  "text": "b",
                  "innerText": "b",
                  "complete": true,
                  "children": []
                }
              ]
            }
          ]
        },
        {
          "startIndex": 9,
          "type": "command",
          "endIndex": 10,
          "text": "c",
          "innerText": "c",
          "complete": true,
          "children": [
            {
              "startIndex": 9,
              "type": "word",
              "endIndex": 10,
              "text": "c",
              "innerText": "c",
              "complete": true,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}

// Case 16
{
  "startIndex": 0,
  "type": "program",
  "endIndex": 12,
  "text": "(a) | b && c",
  "innerText": "(a) | b && c",
  "complete": true,
  "children": [
    {
      "startIndex": 0,
      "type": "list",
      "endIndex": 12,
      "text": "(a) | b && c",
      "innerText": "(a) | b && c",
      "complete": true,
      "children": [
        {
          "startIndex": 0,
          "type": "pipeline",
          "endIndex": 8,
          "text": "(a) | b ",
          "innerText": "(a) | b ",
          "complete": true,
          "children": [
            {
              "startIndex": 0,
              "type": "subshell",
              "endIndex": 3,
              "text": "(a)",
              "innerText": "(a)",
              "complete": true,
              "children": [
                {
                  "startIndex": 1,
                  "type": "command",
                  "endIndex": 2,
                  "text": "a",
                  "innerText": "a",
                  "complete": true,
                  "children": [
                    {
                      "startIndex": 1,
                      "type": "word",
                      "endIndex": 2,
                      "text": "a",
                      "innerText": "a",
                      "complete": true,
                      "children": []
                    }
                  ]
                }
              ]
            },
            {
              "startIndex": 6,
              "type": "command",
              "endIndex": 8,
              "text": "b ",
              "innerText": "b ",
              "complete": true,
              "children": [
                {
                  "startIndex": 6,
                  "type": "word",
                  "endIndex": 7,
                  "text": "b",
                  "innerText": "b",
                  "complete": true,
                  "children": []
                }
              ]
            }
          ]
        },
        {
          "startIndex": 11,
          "type": "command",
          "endIndex": 12,
          "text": "c",
          "innerText": "c",
          "complete": true,
          "children": [
            {
              "startIndex": 11,
              "type": "word",
              "endIndex": 12,
              "text": "c",
              "innerText": "c",
              "complete": true,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}