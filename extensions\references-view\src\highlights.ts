/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cypher-ide from 'cypher-ide';
import { SymbolItemEditorHighlights } from './references-view';

export class EditorHighlights<T> {

	private readonly _decorationType = cypher-ide.window.createTextEditorDecorationType({
		backgroundColor: new cypher-ide.ThemeColor('editor.findMatchHighlightBackground'),
		rangeBehavior: cypher-ide.DecorationRangeBehavior.ClosedClosed,
		overviewRulerLane: cypher-ide.OverviewRulerLane.Center,
		overviewRulerColor: new cypher-ide.ThemeColor('editor.findMatchHighlightBackground'),
	});

	private readonly disposables: cypher-ide.Disposable[] = [];
	private readonly _ignore = new Set<string>();

	constructor(private readonly _view: cypher-ide.TreeView<T>, private readonly _delegate: SymbolItemEditorHighlights<T>) {
		this.disposables.push(
			cypher-ide.workspace.onDidChangeTextDocument(e => this._ignore.add(e.document.uri.toString())),
			cypher-ide.window.onDidChangeActiveTextEditor(() => _view.visible && this.update()),
			_view.onDidChangeVisibility(e => e.visible ? this._show() : this._hide()),
			_view.onDidChangeSelection(() => {
				if (_view.visible) {
					this.update();
				}
			})
		);
		this._show();
	}

	dispose() {
		cypher-ide.Disposable.from(...this.disposables).dispose();
		for (const editor of cypher-ide.window.visibleTextEditors) {
			editor.setDecorations(this._decorationType, []);
		}
	}

	private _show(): void {
		const { activeTextEditor: editor } = cypher-ide.window;
		if (!editor || !editor.viewColumn) {
			return;
		}
		if (this._ignore.has(editor.document.uri.toString())) {
			return;
		}
		const [anchor] = this._view.selection;
		if (!anchor) {
			return;
		}
		const ranges = this._delegate.getEditorHighlights(anchor, editor.document.uri);
		if (ranges) {
			editor.setDecorations(this._decorationType, ranges);
		}
	}

	private _hide(): void {
		for (const editor of cypher-ide.window.visibleTextEditors) {
			editor.setDecorations(this._decorationType, []);
		}
	}

	update(): void {
		this._hide();
		this._show();
	}
}
