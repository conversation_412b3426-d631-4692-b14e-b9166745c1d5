{"name": "theme-defaults", "displayName": "%displayName%", "description": "%description%", "categories": ["Themes"], "version": "1.0.0", "publisher": "cypher-ide", "license": "MIT", "engines": {"cypher-ide": "*"}, "contributes": {"themes": [{"id": "Default Dark+", "label": "%darkPlusColorThemeLabel%", "uiTheme": "vs-dark", "path": "./themes/dark_plus.json"}, {"id": "Default Dark Modern", "label": "%darkModernThemeLabel%", "uiTheme": "vs-dark", "path": "./themes/dark_modern.json"}, {"id": "Default Light+", "label": "%lightPlusColorThemeLabel%", "uiTheme": "vs", "path": "./themes/light_plus.json"}, {"id": "Default Light Modern", "label": "%lightModernThemeLabel%", "uiTheme": "vs", "path": "./themes/light_modern.json"}, {"id": "Visual Studio Dark", "label": "%darkColorThemeLabel%", "uiTheme": "vs-dark", "path": "./themes/dark_vs.json"}, {"id": "Visual Studio Light", "label": "%lightColorThemeLabel%", "uiTheme": "vs", "path": "./themes/light_vs.json"}, {"id": "Default High Contrast", "label": "%hcColorThemeLabel%", "uiTheme": "hc-black", "path": "./themes/hc_black.json"}, {"id": "Default High Contrast Light", "label": "%lightHcColorThemeLabel%", "uiTheme": "hc-light", "path": "./themes/hc_light.json"}], "iconThemes": [{"id": "vs-minimal", "label": "%minimalIconThemeLabel%", "path": "./fileicons/vs_minimal-icon-theme.json"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/cypher-ide.git"}}