/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import { Uri } from 'cypher-ide';

const VALID_DESKTOP_CALLBACK_SCHEMES = [
	'cypher-ide',
	'cypher-ide-insiders',
	// On Windows, some browsers don't seem to redirect back to OSS properly.
	// As a result, you get stuck in the auth flow. We exclude this from the
	// list until we can figure out a way to fix this behavior in browsers.
	// 'cypher-ide',
	'cypher-ide-wsl',
	'cypher-ide-exploration'
];

export function isSupportedClient(uri: Uri): boolean {
	return (
		VALID_DESKTOP_CALLBACK_SCHEMES.includes(uri.scheme) ||
		// cypher-ide.dev & insiders.cypher-ide.dev
		/(?:^|\.)cypher-ide\.dev$/.test(uri.authority) ||
		// github.dev & codespaces
		/(?:^|\.)github\.dev$/.test(uri.authority) ||
		// localhost
		/^localhost:\d+$/.test(uri.authority) ||
		// 127.0.0.1
		/^127\.0\.0\.1:\d+$/.test(uri.authority)
	);
}
