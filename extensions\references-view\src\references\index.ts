/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cypher-ide from 'cypher-ide';
import { SymbolsTree } from '../tree';
import { FileItem, ReferenceItem, ReferencesModel, ReferencesTreeInput } from './model';

export function register(tree: SymbolsTree, context: cypher-ide.ExtensionContext): void {

	function findLocations(title: string, command: string) {
		if (cypher-ide.window.activeTextEditor) {
			const input = new ReferencesTreeInput(title, new cypher-ide.Location(cypher-ide.window.activeTextEditor.document.uri, cypher-ide.window.activeTextEditor.selection.active), command);
			tree.setInput(input);
		}
	}

	context.subscriptions.push(
		cypher-ide.commands.registerCommand('references-view.findReferences', () => findLocations('References', 'cypher-ide.executeReferenceProvider')),
		cypher-ide.commands.registerCommand('references-view.findImplementations', () => findLocations('Implementations', 'cypher-ide.executeImplementationProvider')),
		// --- legacy name
		cypher-ide.commands.registerCommand('references-view.find', (...args: any[]) => cypher-ide.commands.executeCommand('references-view.findReferences', ...args)),
		cypher-ide.commands.registerCommand('references-view.removeReferenceItem', removeReferenceItem),
		cypher-ide.commands.registerCommand('references-view.copy', copyCommand),
		cypher-ide.commands.registerCommand('references-view.copyAll', copyAllCommand),
		cypher-ide.commands.registerCommand('references-view.copyPath', copyPathCommand),
	);


	// --- references.preferredLocation setting

	let showReferencesDisposable: cypher-ide.Disposable | undefined;
	const config = 'references.preferredLocation';
	function updateShowReferences(event?: cypher-ide.ConfigurationChangeEvent) {
		if (event && !event.affectsConfiguration(config)) {
			return;
		}
		const value = cypher-ide.workspace.getConfiguration().get<string>(config);

		showReferencesDisposable?.dispose();
		showReferencesDisposable = undefined;

		if (value === 'view') {
			showReferencesDisposable = cypher-ide.commands.registerCommand('editor.action.showReferences', async (uri: cypher-ide.Uri, position: cypher-ide.Position, locations: cypher-ide.Location[]) => {
				const input = new ReferencesTreeInput(cypher-ide.l10n.t('References'), new cypher-ide.Location(uri, position), 'cypher-ide.executeReferenceProvider', locations);
				tree.setInput(input);
			});
		}
	}
	context.subscriptions.push(cypher-ide.workspace.onDidChangeConfiguration(updateShowReferences));
	context.subscriptions.push({ dispose: () => showReferencesDisposable?.dispose() });
	updateShowReferences();
}

const copyAllCommand = async (item: ReferenceItem | FileItem | unknown) => {
	if (item instanceof ReferenceItem) {
		copyCommand(item.file.model);
	} else if (item instanceof FileItem) {
		copyCommand(item.model);
	}
};

function removeReferenceItem(item: FileItem | ReferenceItem | unknown) {
	if (item instanceof FileItem) {
		item.remove();
	} else if (item instanceof ReferenceItem) {
		item.remove();
	}
}


async function copyCommand(item: ReferencesModel | ReferenceItem | FileItem | unknown) {
	let val: string | undefined;
	if (item instanceof ReferencesModel) {
		val = await item.asCopyText();
	} else if (item instanceof ReferenceItem) {
		val = await item.asCopyText();
	} else if (item instanceof FileItem) {
		val = await item.asCopyText();
	}
	if (val) {
		await cypher-ide.env.clipboard.writeText(val);
	}
}

async function copyPathCommand(item: FileItem | unknown) {
	if (item instanceof FileItem) {
		if (item.uri.scheme === 'file') {
			cypher-ide.env.clipboard.writeText(item.uri.fsPath);
		} else {
			cypher-ide.env.clipboard.writeText(item.uri.toString(true));
		}
	}
}
