/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cypher-ide from 'cypher-ide';
import { BaseServiceConfigurationProvider } from './configuration';

export class BrowserServiceConfigurationProvider extends BaseServiceConfigurationProvider {

	// On browsers, we only support using the built-in TS version
	protected readGlobalTsdk(_configuration: cypher-ide.WorkspaceConfiguration): string | null {
		return null;
	}

	protected readLocalTsdk(_configuration: cypher-ide.WorkspaceConfiguration): string | null {
		return null;
	}

	// On browsers, we don't run TSServer on Node
	protected readLocalNodePath(_configuration: cypher-ide.WorkspaceConfiguration): string | null {
		return null;
	}

	protected override readGlobalNodePath(_configuration: cypher-ide.WorkspaceConfiguration): string | null {
		return null;
	}
}
