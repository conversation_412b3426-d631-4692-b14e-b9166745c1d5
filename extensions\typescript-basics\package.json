{"name": "typescript", "description": "%description%", "displayName": "%displayName%", "version": "1.0.0", "author": "cypher-ide", "publisher": "cypher-ide", "license": "MIT", "engines": {"cypher-ide": "*"}, "scripts": {"update-grammar": "node ./build/update-grammars.mjs"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "typescript", "aliases": ["TypeScript", "ts", "typescript"], "extensions": [".ts", ".cts", ".mts"], "configuration": "./language-configuration.json"}, {"id": "typescriptreact", "aliases": ["TypeScript JSX", "TypeScript React", "tsx"], "extensions": [".tsx"], "configuration": "./language-configuration.json"}, {"id": "jsonc", "filenames": ["tsconfig.json", "jsconfig.json"], "filenamePatterns": ["tsconfig.*.json", "jsconfig.*.json", "tsconfig-*.json", "jsconfig-*.json"]}, {"id": "json", "extensions": [".tsbuildinfo"]}], "grammars": [{"language": "typescript", "scopeName": "source.ts", "path": "./syntaxes/TypeScript.tmLanguage.json", "unbalancedBracketScopes": ["keyword.operator.relational", "storage.type.function.arrow", "keyword.operator.bitwise.shift", "meta.brace.angle", "punctuation.definition.tag", "keyword.operator.assignment.compound.bitwise.ts"], "tokenTypes": {"punctuation.definition.template-expression": "other", "entity.name.type.instance.jsdoc": "other", "entity.name.function.tagged-template": "other", "meta.import string.quoted": "other", "variable.other.jsdoc": "other"}}, {"language": "typescriptreact", "scopeName": "source.tsx", "path": "./syntaxes/TypeScriptReact.tmLanguage.json", "unbalancedBracketScopes": ["keyword.operator.relational", "storage.type.function.arrow", "keyword.operator.bitwise.shift", "punctuation.definition.tag", "keyword.operator.assignment.compound.bitwise.ts"], "embeddedLanguages": {"meta.tag.tsx": "jsx-tags", "meta.tag.without-attributes.tsx": "jsx-tags", "meta.tag.attributes.tsx": "typescriptreact", "meta.embedded.expression.tsx": "typescriptreact"}, "tokenTypes": {"punctuation.definition.template-expression": "other", "entity.name.type.instance.jsdoc": "other", "entity.name.function.tagged-template": "other", "meta.import string.quoted": "other", "variable.other.jsdoc": "other"}}, {"scopeName": "documentation.injection.ts", "path": "./syntaxes/jsdoc.ts.injection.tmLanguage.json", "injectTo": ["source.ts", "source.tsx"]}, {"scopeName": "documentation.injection.js.jsx", "path": "./syntaxes/jsdoc.js.injection.tmLanguage.json", "injectTo": ["source.js", "source.js.jsx"]}], "semanticTokenScopes": [{"language": "typescript", "scopes": {"property": ["variable.other.property.ts"], "property.readonly": ["variable.other.constant.property.ts"], "variable": ["variable.other.readwrite.ts"], "variable.readonly": ["variable.other.constant.object.ts"], "function": ["entity.name.function.ts"], "namespace": ["entity.name.type.module.ts"], "variable.defaultLibrary": ["support.variable.ts"], "function.defaultLibrary": ["support.function.ts"]}}, {"language": "typescriptreact", "scopes": {"property": ["variable.other.property.tsx"], "property.readonly": ["variable.other.constant.property.tsx"], "variable": ["variable.other.readwrite.tsx"], "variable.readonly": ["variable.other.constant.object.tsx"], "function": ["entity.name.function.tsx"], "namespace": ["entity.name.type.module.tsx"], "variable.defaultLibrary": ["support.variable.tsx"], "function.defaultLibrary": ["support.function.tsx"]}}], "snippets": [{"language": "typescript", "path": "./snippets/typescript.code-snippets"}, {"language": "typescriptreact", "path": "./snippets/typescript.code-snippets"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/cypher-ide.git"}}