{"name": "shaderlab", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "cypher-ide", "license": "MIT", "engines": {"cypher-ide": "*"}, "scripts": {"update-grammar": "node ../node_modules/cypher-ide-grammar-updater/bin tgjones/shaders-tmLanguage grammars/shaderlab.json ./syntaxes/shaderlab.tmLanguage.json"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "shaderlab", "extensions": [".shader"], "aliases": ["ShaderLab", "shaderlab"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "shaderlab", "path": "./syntaxes/shaderlab.tmLanguage.json", "scopeName": "source.shaderlab"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/cypher-ide.git"}}