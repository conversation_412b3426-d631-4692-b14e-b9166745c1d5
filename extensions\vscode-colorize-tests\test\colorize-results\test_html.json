[{"c": "<", "t": "text.html.derivative meta.tag.structure.html.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "html", "t": "text.html.derivative meta.tag.structure.html.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.tag.structure.html.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "<", "t": "text.html.derivative meta.tag.structure.head.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "head", "t": "text.html.derivative meta.tag.structure.head.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.tag.structure.head.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "\t", "t": "text.html.derivative", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.derivative meta.tag.metadata.meta.void.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "meta", "t": "text.html.derivative meta.tag.metadata.meta.void.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.derivative meta.tag.metadata.meta.void.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "charset", "t": "text.html.derivative meta.tag.metadata.meta.void.html meta.attribute.charset.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.derivative meta.tag.metadata.meta.void.html meta.attribute.charset.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.derivative meta.tag.metadata.meta.void.html meta.attribute.charset.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "utf-8", "t": "text.html.derivative meta.tag.metadata.meta.void.html meta.attribute.charset.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.derivative meta.tag.metadata.meta.void.html meta.attribute.charset.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": ">", "t": "text.html.derivative meta.tag.metadata.meta.void.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "\t", "t": "text.html.derivative", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.derivative meta.tag.metadata.title.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "title", "t": "text.html.derivative meta.tag.metadata.title.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.tag.metadata.title.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "CypherIDE Tests", "t": "text.html.derivative", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "</", "t": "text.html.derivative meta.tag.metadata.title.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "title", "t": "text.html.derivative meta.tag.metadata.title.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.tag.metadata.title.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "\t", "t": "text.html.derivative", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.derivative meta.tag.metadata.link.void.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "link", "t": "text.html.derivative meta.tag.metadata.link.void.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.derivative meta.tag.metadata.link.void.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "href", "t": "text.html.derivative meta.tag.metadata.link.void.html meta.attribute.href.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.derivative meta.tag.metadata.link.void.html meta.attribute.href.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.derivative meta.tag.metadata.link.void.html meta.attribute.href.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "https://cdn.rawgit.com/mochajs/mocha/2.2.5/mocha.css", "t": "text.html.derivative meta.tag.metadata.link.void.html meta.attribute.href.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.derivative meta.tag.metadata.link.void.html meta.attribute.href.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": " ", "t": "text.html.derivative meta.tag.metadata.link.void.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "rel", "t": "text.html.derivative meta.tag.metadata.link.void.html meta.attribute.rel.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.derivative meta.tag.metadata.link.void.html meta.attribute.rel.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.derivative meta.tag.metadata.link.void.html meta.attribute.rel.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "stylesheet", "t": "text.html.derivative meta.tag.metadata.link.void.html meta.attribute.rel.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.derivative meta.tag.metadata.link.void.html meta.attribute.rel.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": " ", "t": "text.html.derivative meta.tag.metadata.link.void.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "/>", "t": "text.html.derivative meta.tag.metadata.link.void.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "\t", "t": "text.html.derivative punctuation.whitespace.embedded.leading.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.style.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "style", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.style.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.style.start.html", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "type", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.style.start.html meta.attribute.type.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.style.start.html meta.attribute.type.html punctuation.separator.key-value.html", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "\"", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.style.start.html meta.attribute.type.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "text/css", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.style.start.html meta.attribute.type.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.style.start.html meta.attribute.type.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": ">", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.style.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "\t\t", "t": "text.html.derivative meta.embedded.block.html source.css", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "body", "t": "text.html.derivative meta.embedded.block.html source.css meta.selector.css entity.name.tag.css", "r": {"dark_plus": "entity.name.tag.css: #D7BA7D", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag.css: #D7BA7D", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag.css: #D7BA7D", "dark_modern": "entity.name.tag.css: #D7BA7D", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.derivative meta.embedded.block.html source.css", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "{", "t": "text.html.derivative meta.embedded.block.html source.css meta.property-list.css punctuation.section.property-list.begin.bracket.curly.css", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "\t\t\t", "t": "text.html.derivative meta.embedded.block.html source.css meta.property-list.css", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "color", "t": "text.html.derivative meta.embedded.block.html source.css meta.property-list.css meta.property-name.css support.type.property-name.css", "r": {"dark_plus": "support.type.property-name: #9CDCFE", "light_plus": "support.type.property-name: #E50000", "dark_vs": "support.type.property-name: #9CDCFE", "light_vs": "support.type.property-name: #E50000", "hc_black": "support.type.property-name: #D4D4D4", "dark_modern": "support.type.property-name: #9CDCFE", "hc_light": "support.type.property-name: #264F78", "light_modern": "support.type.property-name: #E50000"}}, {"c": ":", "t": "text.html.derivative meta.embedded.block.html source.css meta.property-list.css punctuation.separator.key-value.css", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": " ", "t": "text.html.derivative meta.embedded.block.html source.css meta.property-list.css", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "purple", "t": "text.html.derivative meta.embedded.block.html source.css meta.property-list.css meta.property-value.css support.constant.color.w3c-standard-color-name.css", "r": {"dark_plus": "support.constant.color: #CE9178", "light_plus": "support.constant.color: #0451A5", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "support.constant.color: #0451A5", "hc_black": "support.constant.color: #CE9178", "dark_modern": "support.constant.color: #CE9178", "hc_light": "support.constant.color: #0451A5", "light_modern": "support.constant.color: #0451A5"}}, {"c": ";", "t": "text.html.derivative meta.embedded.block.html source.css meta.property-list.css punctuation.terminator.rule.css", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "\t\t\t", "t": "text.html.derivative meta.embedded.block.html source.css meta.property-list.css", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "background-color", "t": "text.html.derivative meta.embedded.block.html source.css meta.property-list.css meta.property-name.css support.type.property-name.css", "r": {"dark_plus": "support.type.property-name: #9CDCFE", "light_plus": "support.type.property-name: #E50000", "dark_vs": "support.type.property-name: #9CDCFE", "light_vs": "support.type.property-name: #E50000", "hc_black": "support.type.property-name: #D4D4D4", "dark_modern": "support.type.property-name: #9CDCFE", "hc_light": "support.type.property-name: #264F78", "light_modern": "support.type.property-name: #E50000"}}, {"c": ":", "t": "text.html.derivative meta.embedded.block.html source.css meta.property-list.css punctuation.separator.key-value.css", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": " ", "t": "text.html.derivative meta.embedded.block.html source.css meta.property-list.css", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "#", "t": "text.html.derivative meta.embedded.block.html source.css meta.property-list.css meta.property-value.css constant.other.color.rgb-value.hex.css punctuation.definition.constant.css", "r": {"dark_plus": "constant.other.color.rgb-value: #CE9178", "light_plus": "constant.other.color.rgb-value: #0451A5", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "constant.other.color.rgb-value: #0451A5", "hc_black": "constant.other.color.rgb-value: #CE9178", "dark_modern": "constant.other.color.rgb-value: #CE9178", "hc_light": "constant.other.color.rgb-value: #0451A5", "light_modern": "constant.other.color.rgb-value: #0451A5"}}, {"c": "d8da3d", "t": "text.html.derivative meta.embedded.block.html source.css meta.property-list.css meta.property-value.css constant.other.color.rgb-value.hex.css", "r": {"dark_plus": "constant.other.color.rgb-value: #CE9178", "light_plus": "constant.other.color.rgb-value: #0451A5", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "constant.other.color.rgb-value: #0451A5", "hc_black": "constant.other.color.rgb-value: #CE9178", "dark_modern": "constant.other.color.rgb-value: #CE9178", "hc_light": "constant.other.color.rgb-value: #0451A5", "light_modern": "constant.other.color.rgb-value: #0451A5"}}, {"c": ";", "t": "text.html.derivative meta.embedded.block.html source.css meta.property-list.css punctuation.terminator.rule.css", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "\t\t", "t": "text.html.derivative meta.embedded.block.html source.css meta.property-list.css", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "}", "t": "text.html.derivative meta.embedded.block.html source.css meta.property-list.css punctuation.section.property-list.end.bracket.curly.css", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "\t", "t": "text.html.derivative meta.embedded.block.html source.css", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "<", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.style.end.html punctuation.definition.tag.begin.html source.css-ignored-cypher-ide", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "/", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.style.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "style", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.style.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.style.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "</", "t": "text.html.derivative meta.tag.structure.head.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "head", "t": "text.html.derivative meta.tag.structure.head.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.tag.structure.head.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "<", "t": "text.html.derivative meta.tag.structure.body.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "body", "t": "text.html.derivative meta.tag.structure.body.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.tag.structure.body.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "\t", "t": "text.html.derivative", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.derivative meta.tag.structure.div.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "div", "t": "text.html.derivative meta.tag.structure.div.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.derivative meta.tag.structure.div.start.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "id", "t": "text.html.derivative meta.tag.structure.div.start.html meta.attribute.id.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.derivative meta.tag.structure.div.start.html meta.attribute.id.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.derivative meta.tag.structure.div.start.html meta.attribute.id.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "mocha", "t": "text.html.derivative meta.tag.structure.div.start.html meta.attribute.id.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.derivative meta.tag.structure.div.start.html meta.attribute.id.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": ">", "t": "text.html.derivative meta.tag.structure.div.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "</", "t": "text.html.derivative meta.tag.structure.div.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "div", "t": "text.html.derivative meta.tag.structure.div.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.tag.structure.div.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "\t", "t": "text.html.derivative", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<!--", "t": "text.html.derivative comment.block.html punctuation.definition.comment.html", "r": {"dark_plus": "comment: #6A9955", "light_plus": "comment: #008000", "dark_vs": "comment: #6A9955", "light_vs": "comment: #008000", "hc_black": "comment: #7CA668", "dark_modern": "comment: #6A9955", "hc_light": "comment: #515151", "light_modern": "comment: #008000"}}, {"c": "<script src=\"https://cdn.rawgit.com/jquery/jquery/2.1.4/dist/jquery.min.js\"></script>", "t": "text.html.derivative comment.block.html", "r": {"dark_plus": "comment: #6A9955", "light_plus": "comment: #008000", "dark_vs": "comment: #6A9955", "light_vs": "comment: #008000", "hc_black": "comment: #7CA668", "dark_modern": "comment: #6A9955", "hc_light": "comment: #515151", "light_modern": "comment: #008000"}}, {"c": "\t<script src=\"https://cdn.rawgit.com/Automattic/expect.js/0.3.1/index.js\"></script>", "t": "text.html.derivative comment.block.html", "r": {"dark_plus": "comment: #6A9955", "light_plus": "comment: #008000", "dark_vs": "comment: #6A9955", "light_vs": "comment: #008000", "hc_black": "comment: #7CA668", "dark_modern": "comment: #6A9955", "hc_light": "comment: #515151", "light_modern": "comment: #008000"}}, {"c": "-->", "t": "text.html.derivative comment.block.html punctuation.definition.comment.html", "r": {"dark_plus": "comment: #6A9955", "light_plus": "comment: #008000", "dark_vs": "comment: #6A9955", "light_vs": "comment: #008000", "hc_black": "comment: #7CA668", "dark_modern": "comment: #6A9955", "hc_light": "comment: #515151", "light_modern": "comment: #008000"}}, {"c": "\t", "t": "text.html.derivative punctuation.whitespace.embedded.leading.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "script", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "src", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html meta.attribute.src.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html meta.attribute.src.html punctuation.separator.key-value.html", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "\"", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html meta.attribute.src.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "/out/vs/loader.js", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html meta.attribute.src.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html meta.attribute.src.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": ">", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "<", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.end.html punctuation.definition.tag.begin.html source.js-ignored-cypher-ide", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "/", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "script", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "\t", "t": "text.html.derivative punctuation.whitespace.embedded.leading.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "script", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "src", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html meta.attribute.src.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html meta.attribute.src.html punctuation.separator.key-value.html", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "\"", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html meta.attribute.src.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "https://cdn.rawgit.com/mochajs/mocha/2.2.5/mocha.js", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html meta.attribute.src.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html meta.attribute.src.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": ">", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "<", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.end.html punctuation.definition.tag.begin.html source.js-ignored-cypher-ide", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "/", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "script", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "\t", "t": "text.html.derivative punctuation.whitespace.embedded.leading.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "script", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "\t\t", "t": "text.html.derivative meta.embedded.block.html source.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "mocha", "t": "text.html.derivative meta.embedded.block.html source.js meta.function-call.js variable.other.object.js", "r": {"dark_plus": "variable: #9CDCFE", "light_plus": "variable: #001080", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "variable: #9CDCFE", "dark_modern": "variable: #9CDCFE", "hc_light": "variable: #001080", "light_modern": "variable: #001080"}}, {"c": ".", "t": "text.html.derivative meta.embedded.block.html source.js meta.function-call.js punctuation.accessor.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "setup", "t": "text.html.derivative meta.embedded.block.html source.js meta.function-call.js entity.name.function.js", "r": {"dark_plus": "entity.name.function: #DCDCAA", "light_plus": "entity.name.function: #795E26", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "entity.name.function: #DCDCAA", "dark_modern": "entity.name.function: #DCDCAA", "hc_light": "entity.name.function: #5E2CBC", "light_modern": "entity.name.function: #795E26"}}, {"c": "(", "t": "text.html.derivative meta.embedded.block.html source.js meta.brace.round.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "'", "t": "text.html.derivative meta.embedded.block.html source.js string.quoted.single.js punctuation.definition.string.begin.js", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": "tdd", "t": "text.html.derivative meta.embedded.block.html source.js string.quoted.single.js", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": "'", "t": "text.html.derivative meta.embedded.block.html source.js string.quoted.single.js punctuation.definition.string.end.js", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": ")", "t": "text.html.derivative meta.embedded.block.html source.js meta.brace.round.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": ";", "t": "text.html.derivative meta.embedded.block.html source.js punctuation.terminator.statement.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "\t\t", "t": "text.html.derivative meta.embedded.block.html source.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "require", "t": "text.html.derivative meta.embedded.block.html source.js meta.function-call.js variable.other.object.js", "r": {"dark_plus": "variable: #9CDCFE", "light_plus": "variable: #001080", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "variable: #9CDCFE", "dark_modern": "variable: #9CDCFE", "hc_light": "variable: #001080", "light_modern": "variable: #001080"}}, {"c": ".", "t": "text.html.derivative meta.embedded.block.html source.js meta.function-call.js punctuation.accessor.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "config", "t": "text.html.derivative meta.embedded.block.html source.js meta.function-call.js entity.name.function.js", "r": {"dark_plus": "entity.name.function: #DCDCAA", "light_plus": "entity.name.function: #795E26", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "entity.name.function: #DCDCAA", "dark_modern": "entity.name.function: #DCDCAA", "hc_light": "entity.name.function: #5E2CBC", "light_modern": "entity.name.function: #795E26"}}, {"c": "(", "t": "text.html.derivative meta.embedded.block.html source.js meta.brace.round.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "{", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js punctuation.definition.block.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "\t\t\t", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "baseUrl", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js meta.object-literal.key.js", "r": {"dark_plus": "meta.object-literal.key: #9CDCFE", "light_plus": "meta.object-literal.key: #001080", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.object-literal.key: #9CDCFE", "dark_modern": "meta.object-literal.key: #9CDCFE", "hc_light": "meta.object-literal.key: #001080", "light_modern": "meta.object-literal.key: #001080"}}, {"c": ":", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js meta.object-literal.key.js punctuation.separator.key-value.js", "r": {"dark_plus": "meta.object-literal.key: #9CDCFE", "light_plus": "meta.object-literal.key: #001080", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.object-literal.key: #9CDCFE", "dark_modern": "meta.object-literal.key: #9CDCFE", "hc_light": "meta.object-literal.key: #001080", "light_modern": "meta.object-literal.key: #001080"}}, {"c": " ", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "'", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js string.quoted.single.js punctuation.definition.string.begin.js", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": "/out", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js string.quoted.single.js", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": "'", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js string.quoted.single.js punctuation.definition.string.end.js", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": ",", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js punctuation.separator.comma.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "\t\t\t", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "paths", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js meta.object-literal.key.js", "r": {"dark_plus": "meta.object-literal.key: #9CDCFE", "light_plus": "meta.object-literal.key: #001080", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.object-literal.key: #9CDCFE", "dark_modern": "meta.object-literal.key: #9CDCFE", "hc_light": "meta.object-literal.key: #001080", "light_modern": "meta.object-literal.key: #001080"}}, {"c": ":", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js meta.object-literal.key.js punctuation.separator.key-value.js", "r": {"dark_plus": "meta.object-literal.key: #9CDCFE", "light_plus": "meta.object-literal.key: #001080", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.object-literal.key: #9CDCFE", "dark_modern": "meta.object-literal.key: #9CDCFE", "hc_light": "meta.object-literal.key: #001080", "light_modern": "meta.object-literal.key: #001080"}}, {"c": " ", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "{", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js meta.objectliteral.js punctuation.definition.block.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "\t\t\t\t", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js meta.objectliteral.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "assert", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js meta.objectliteral.js meta.object.member.js meta.object-literal.key.js", "r": {"dark_plus": "meta.object-literal.key: #9CDCFE", "light_plus": "meta.object-literal.key: #001080", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.object-literal.key: #9CDCFE", "dark_modern": "meta.object-literal.key: #9CDCFE", "hc_light": "meta.object-literal.key: #001080", "light_modern": "meta.object-literal.key: #001080"}}, {"c": ":", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js meta.objectliteral.js meta.object.member.js meta.object-literal.key.js punctuation.separator.key-value.js", "r": {"dark_plus": "meta.object-literal.key: #9CDCFE", "light_plus": "meta.object-literal.key: #001080", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.object-literal.key: #9CDCFE", "dark_modern": "meta.object-literal.key: #9CDCFE", "hc_light": "meta.object-literal.key: #001080", "light_modern": "meta.object-literal.key: #001080"}}, {"c": " ", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js meta.objectliteral.js meta.object.member.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "'", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js meta.objectliteral.js meta.object.member.js string.quoted.single.js punctuation.definition.string.begin.js", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": "/test/assert.js", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js meta.objectliteral.js meta.object.member.js string.quoted.single.js", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": "'", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js meta.objectliteral.js meta.object.member.js string.quoted.single.js punctuation.definition.string.end.js", "r": {"dark_plus": "string: #CE9178", "light_plus": "string: #A31515", "dark_vs": "string: #CE9178", "light_vs": "string: #A31515", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string: #0F4A85", "light_modern": "string: #A31515"}}, {"c": "\t\t\t", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js meta.objectliteral.js meta.object.member.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "}", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js meta.objectliteral.js punctuation.definition.block.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "\t\t", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.object.member.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "}", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js punctuation.definition.block.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": ")", "t": "text.html.derivative meta.embedded.block.html source.js meta.brace.round.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": ";", "t": "text.html.derivative meta.embedded.block.html source.js punctuation.terminator.statement.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "\t\t", "t": "text.html.derivative meta.embedded.block.html source.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "require", "t": "text.html.derivative meta.embedded.block.html source.js meta.function-call.js entity.name.function.js", "r": {"dark_plus": "entity.name.function: #DCDCAA", "light_plus": "entity.name.function: #795E26", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "entity.name.function: #DCDCAA", "dark_modern": "entity.name.function: #DCDCAA", "hc_light": "entity.name.function: #5E2CBC", "light_modern": "entity.name.function: #795E26"}}, {"c": "(", "t": "text.html.derivative meta.embedded.block.html source.js meta.brace.round.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "{", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js punctuation.definition.block.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "{", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.block.js punctuation.definition.block.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": " ", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.block.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "modules", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.block.js variable.other.readwrite.js", "r": {"dark_plus": "variable: #9CDCFE", "light_plus": "variable: #001080", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "variable: #9CDCFE", "dark_modern": "variable: #9CDCFE", "hc_light": "variable: #001080", "light_modern": "variable: #001080"}}, {"c": " ", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.block.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "}", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js meta.block.js punctuation.definition.block.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "}", "t": "text.html.derivative meta.embedded.block.html source.js meta.objectliteral.js punctuation.definition.block.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": ",", "t": "text.html.derivative meta.embedded.block.html source.js punctuation.separator.comma.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": " ", "t": "text.html.derivative meta.embedded.block.html source.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "function", "t": "text.html.derivative meta.embedded.block.html source.js meta.function.expression.js storage.type.function.js", "r": {"dark_plus": "storage.type: #569CD6", "light_plus": "storage.type: #0000FF", "dark_vs": "storage.type: #569CD6", "light_vs": "storage.type: #0000FF", "hc_black": "storage.type: #569CD6", "dark_modern": "storage.type: #569CD6", "hc_light": "storage.type: #0F4A85", "light_modern": "storage.type: #0000FF"}}, {"c": " ", "t": "text.html.derivative meta.embedded.block.html source.js meta.function.expression.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "(", "t": "text.html.derivative meta.embedded.block.html source.js meta.function.expression.js meta.parameters.js punctuation.definition.parameters.begin.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": ")", "t": "text.html.derivative meta.embedded.block.html source.js meta.function.expression.js meta.parameters.js punctuation.definition.parameters.end.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": " ", "t": "text.html.derivative meta.embedded.block.html source.js meta.function.expression.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "{", "t": "text.html.derivative meta.embedded.block.html source.js meta.function.expression.js meta.block.js punctuation.definition.block.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "\t\t\t", "t": "text.html.derivative meta.embedded.block.html source.js meta.function.expression.js meta.block.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "mocha", "t": "text.html.derivative meta.embedded.block.html source.js meta.function.expression.js meta.block.js meta.function-call.js variable.other.object.js", "r": {"dark_plus": "variable: #9CDCFE", "light_plus": "variable: #001080", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "variable: #9CDCFE", "dark_modern": "variable: #9CDCFE", "hc_light": "variable: #001080", "light_modern": "variable: #001080"}}, {"c": ".", "t": "text.html.derivative meta.embedded.block.html source.js meta.function.expression.js meta.block.js meta.function-call.js punctuation.accessor.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "run", "t": "text.html.derivative meta.embedded.block.html source.js meta.function.expression.js meta.block.js meta.function-call.js entity.name.function.js", "r": {"dark_plus": "entity.name.function: #DCDCAA", "light_plus": "entity.name.function: #795E26", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "entity.name.function: #DCDCAA", "dark_modern": "entity.name.function: #DCDCAA", "hc_light": "entity.name.function: #5E2CBC", "light_modern": "entity.name.function: #795E26"}}, {"c": "()", "t": "text.html.derivative meta.embedded.block.html source.js meta.function.expression.js meta.block.js meta.brace.round.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": ";", "t": "text.html.derivative meta.embedded.block.html source.js meta.function.expression.js meta.block.js punctuation.terminator.statement.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "\t\t", "t": "text.html.derivative meta.embedded.block.html source.js meta.function.expression.js meta.block.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "}", "t": "text.html.derivative meta.embedded.block.html source.js meta.function.expression.js meta.block.js punctuation.definition.block.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": ")", "t": "text.html.derivative meta.embedded.block.html source.js meta.brace.round.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": ";", "t": "text.html.derivative meta.embedded.block.html source.js punctuation.terminator.statement.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "\t", "t": "text.html.derivative meta.embedded.block.html source.js", "r": {"dark_plus": "meta.embedded: #D4D4D4", "light_plus": "meta.embedded: #000000", "dark_vs": "meta.embedded: #D4D4D4", "light_vs": "meta.embedded: #000000", "hc_black": "meta.embedded: #FFFFFF", "dark_modern": "meta.embedded: #D4D4D4", "hc_light": "meta.embedded: #292929", "light_modern": "meta.embedded: #000000"}}, {"c": "<", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.end.html punctuation.definition.tag.begin.html source.js-ignored-cypher-ide", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "/", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "script", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.embedded.block.html meta.tag.metadata.script.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "\t", "t": "text.html.derivative", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.derivative meta.tag.structure.div.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "div", "t": "text.html.derivative meta.tag.structure.div.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.derivative meta.tag.structure.div.start.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "class", "t": "text.html.derivative meta.tag.structure.div.start.html meta.attribute.class.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.derivative meta.tag.structure.div.start.html meta.attribute.class.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.derivative meta.tag.structure.div.start.html meta.attribute.class.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "js-stale-session-flash stale-session-flash flash flash-warn flash-banner hidden", "t": "text.html.derivative meta.tag.structure.div.start.html meta.attribute.class.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.derivative meta.tag.structure.div.start.html meta.attribute.class.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": ">", "t": "text.html.derivative meta.tag.structure.div.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "\t\t", "t": "text.html.derivative", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.derivative meta.tag.inline.span.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "span", "t": "text.html.derivative meta.tag.inline.span.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.derivative meta.tag.inline.span.start.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "class", "t": "text.html.derivative meta.tag.inline.span.start.html meta.attribute.class.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.derivative meta.tag.inline.span.start.html meta.attribute.class.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "codicon", "t": "text.html.derivative meta.tag.inline.span.start.html meta.attribute.class.html string.unquoted.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.unquoted.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.unquoted.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.unquoted.html: #0F4A85", "light_modern": "string.unquoted.html: #0000FF"}}, {"c": ">", "t": "text.html.derivative meta.tag.inline.span.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "</", "t": "text.html.derivative meta.tag.inline.span.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "span", "t": "text.html.derivative meta.tag.inline.span.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.tag.inline.span.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "\t\t", "t": "text.html.derivative", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.derivative meta.tag.inline.span.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "span", "t": "text.html.derivative meta.tag.inline.span.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.derivative meta.tag.inline.span.start.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "class", "t": "text.html.derivative meta.tag.inline.span.start.html meta.attribute.class.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.derivative meta.tag.inline.span.start.html meta.attribute.class.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.derivative meta.tag.inline.span.start.html meta.attribute.class.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "signed-in-tab-flash", "t": "text.html.derivative meta.tag.inline.span.start.html meta.attribute.class.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.derivative meta.tag.inline.span.start.html meta.attribute.class.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": ">", "t": "text.html.derivative meta.tag.inline.span.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "You signed in with another tab or window. ", "t": "text.html.derivative", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.derivative meta.tag.inline.a.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "a", "t": "text.html.derivative meta.tag.inline.a.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.derivative meta.tag.inline.a.start.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "href", "t": "text.html.derivative meta.tag.inline.a.start.html meta.attribute.href.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.derivative meta.tag.inline.a.start.html meta.attribute.href.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.derivative meta.tag.inline.a.start.html meta.attribute.href.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.derivative meta.tag.inline.a.start.html meta.attribute.href.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": ">", "t": "text.html.derivative meta.tag.inline.a.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "Reload", "t": "text.html.derivative", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "</", "t": "text.html.derivative meta.tag.inline.a.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "a", "t": "text.html.derivative meta.tag.inline.a.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.tag.inline.a.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": " to refresh your session.", "t": "text.html.derivative", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "</", "t": "text.html.derivative meta.tag.inline.span.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "span", "t": "text.html.derivative meta.tag.inline.span.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.tag.inline.span.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "\t\t", "t": "text.html.derivative", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.derivative meta.tag.inline.span.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "span", "t": "text.html.derivative meta.tag.inline.span.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.derivative meta.tag.inline.span.start.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "class", "t": "text.html.derivative meta.tag.inline.span.start.html meta.attribute.class.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.derivative meta.tag.inline.span.start.html meta.attribute.class.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.derivative meta.tag.inline.span.start.html meta.attribute.class.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "signed-out-tab-flash", "t": "text.html.derivative meta.tag.inline.span.start.html meta.attribute.class.html string.quoted.double.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.derivative meta.tag.inline.span.start.html meta.attribute.class.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": ">", "t": "text.html.derivative meta.tag.inline.span.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "You signed out in another tab or window. ", "t": "text.html.derivative", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "<", "t": "text.html.derivative meta.tag.inline.a.start.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "a", "t": "text.html.derivative meta.tag.inline.a.start.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": " ", "t": "text.html.derivative meta.tag.inline.a.start.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "href", "t": "text.html.derivative meta.tag.inline.a.start.html meta.attribute.href.html entity.other.attribute-name.html", "r": {"dark_plus": "entity.other.attribute-name: #9CDCFE", "light_plus": "entity.other.attribute-name: #E50000", "dark_vs": "entity.other.attribute-name: #9CDCFE", "light_vs": "entity.other.attribute-name: #E50000", "hc_black": "entity.other.attribute-name: #9CDCFE", "dark_modern": "entity.other.attribute-name: #9CDCFE", "hc_light": "entity.other.attribute-name: #264F78", "light_modern": "entity.other.attribute-name: #E50000"}}, {"c": "=", "t": "text.html.derivative meta.tag.inline.a.start.html meta.attribute.href.html punctuation.separator.key-value.html", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "\"", "t": "text.html.derivative meta.tag.inline.a.start.html meta.attribute.href.html string.quoted.double.html punctuation.definition.string.begin.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": "\"", "t": "text.html.derivative meta.tag.inline.a.start.html meta.attribute.href.html string.quoted.double.html punctuation.definition.string.end.html", "r": {"dark_plus": "string: #CE9178", "light_plus": "string.quoted.double.html: #0000FF", "dark_vs": "string: #CE9178", "light_vs": "string.quoted.double.html: #0000FF", "hc_black": "string: #CE9178", "dark_modern": "string: #CE9178", "hc_light": "string.quoted.double.html: #0F4A85", "light_modern": "string.quoted.double.html: #0000FF"}}, {"c": ">", "t": "text.html.derivative meta.tag.inline.a.start.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "Reload", "t": "text.html.derivative", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "</", "t": "text.html.derivative meta.tag.inline.a.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "a", "t": "text.html.derivative meta.tag.inline.a.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.tag.inline.a.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": " to refresh your session.", "t": "text.html.derivative", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "</", "t": "text.html.derivative meta.tag.inline.span.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "span", "t": "text.html.derivative meta.tag.inline.span.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.tag.inline.span.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "\t", "t": "text.html.derivative", "r": {"dark_plus": "default: #D4D4D4", "light_plus": "default: #000000", "dark_vs": "default: #D4D4D4", "light_vs": "default: #000000", "hc_black": "default: #FFFFFF", "dark_modern": "default: #CCCCCC", "hc_light": "default: #292929", "light_modern": "default: #3B3B3B"}}, {"c": "</", "t": "text.html.derivative meta.tag.structure.div.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "div", "t": "text.html.derivative meta.tag.structure.div.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.tag.structure.div.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "</", "t": "text.html.derivative meta.tag.structure.body.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "body", "t": "text.html.derivative meta.tag.structure.body.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.tag.structure.body.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "</", "t": "text.html.derivative meta.tag.structure.html.end.html punctuation.definition.tag.begin.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}, {"c": "html", "t": "text.html.derivative meta.tag.structure.html.end.html entity.name.tag.html", "r": {"dark_plus": "entity.name.tag: #569CD6", "light_plus": "entity.name.tag: #800000", "dark_vs": "entity.name.tag: #569CD6", "light_vs": "entity.name.tag: #800000", "hc_black": "entity.name.tag: #569CD6", "dark_modern": "entity.name.tag: #569CD6", "hc_light": "entity.name.tag: #0F4A85", "light_modern": "entity.name.tag: #800000"}}, {"c": ">", "t": "text.html.derivative meta.tag.structure.html.end.html punctuation.definition.tag.end.html", "r": {"dark_plus": "punctuation.definition.tag: #808080", "light_plus": "punctuation.definition.tag: #800000", "dark_vs": "punctuation.definition.tag: #808080", "light_vs": "punctuation.definition.tag: #800000", "hc_black": "punctuation.definition.tag: #808080", "dark_modern": "punctuation.definition.tag: #808080", "hc_light": "punctuation.definition.tag: #0F4A85", "light_modern": "punctuation.definition.tag: #800000"}}]