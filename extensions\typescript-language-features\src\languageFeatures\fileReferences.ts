/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cypher-ide from 'cypher-ide';
import { Command, CommandManager } from '../commands/commandManager';
import { isSupportedLanguageMode } from '../configuration/languageIds';
import { API } from '../tsServer/api';
import * as typeConverters from '../typeConverters';
import { ITypeScriptServiceClient } from '../typescriptService';


class FileReferencesCommand implements Command {

	public static readonly context = 'tsSupportsFileReferences';
	public static readonly minVersion = API.v420;

	public readonly id = 'typescript.findAllFileReferences';

	public constructor(
		private readonly client: ITypeScriptServiceClient
	) { }

	public async execute(resource?: cypher-ide.Uri) {
		if (this.client.apiVersion.lt(FileReferencesCommand.minVersion)) {
			cypher-ide.window.showErrorMessage(cypher-ide.l10n.t("Find file references failed. Requires TypeScript 4.2+."));
			return;
		}

		resource ??= cypher-ide.window.activeTextEditor?.document.uri;
		if (!resource) {
			cypher-ide.window.showErrorMessage(cypher-ide.l10n.t("Find file references failed. No resource provided."));
			return;
		}

		const document = await cypher-ide.workspace.openTextDocument(resource);
		if (!isSupportedLanguageMode(document)) {
			cypher-ide.window.showErrorMessage(cypher-ide.l10n.t("Find file references failed. Unsupported file type."));
			return;
		}

		const openedFiledPath = this.client.toOpenTsFilePath(document);
		if (!openedFiledPath) {
			cypher-ide.window.showErrorMessage(cypher-ide.l10n.t("Find file references failed. Unknown file type."));
			return;
		}

		await cypher-ide.window.withProgress({
			location: cypher-ide.ProgressLocation.Window,
			title: cypher-ide.l10n.t("Finding file references")
		}, async (_progress, token) => {

			const response = await this.client.execute('fileReferences', {
				file: openedFiledPath
			}, token);
			if (response.type !== 'response' || !response.body) {
				return;
			}

			const locations: cypher-ide.Location[] = response.body.refs.map(reference =>
				typeConverters.Location.fromTextSpan(this.client.toResource(reference.file), reference));

			const config = cypher-ide.workspace.getConfiguration('references');
			const existingSetting = config.inspect<string>('preferredLocation');

			await config.update('preferredLocation', 'view');
			try {
				await cypher-ide.commands.executeCommand('editor.action.showReferences', resource, new cypher-ide.Position(0, 0), locations);
			} finally {
				await config.update('preferredLocation', existingSetting?.workspaceFolderValue ?? existingSetting?.workspaceValue);
			}
		});
	}
}


export function register(
	client: ITypeScriptServiceClient,
	commandManager: CommandManager
) {
	function updateContext() {
		cypher-ide.commands.executeCommand('setContext', FileReferencesCommand.context, client.apiVersion.gte(FileReferencesCommand.minVersion));
	}
	updateContext();

	commandManager.register(new FileReferencesCommand(client));
	return client.onTsServerStarted(() => updateContext());
}
