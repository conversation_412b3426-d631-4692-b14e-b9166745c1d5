/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cypher-ide from 'cypher-ide';

export default class MergeConflictContentProvider implements cypher-ide.TextDocumentContentProvider, cypher-ide.Disposable {

	static scheme = 'merge-conflict.conflict-diff';

	constructor(private context: cypher-ide.ExtensionContext) {
	}

	begin() {
		this.context.subscriptions.push(
			cypher-ide.workspace.registerTextDocumentContentProvider(MergeConflictContentProvider.scheme, this)
		);
	}

	dispose() {
	}

	async provideTextDocumentContent(uri: cypher-ide.Uri): Promise<string | null> {
		try {
			const { scheme, ranges } = JSON.parse(uri.query) as { scheme: string; ranges: [{ line: number; character: number }[], { line: number; character: number }[]][] };

			// complete diff
			const document = await cypher-ide.workspace.openTextDocument(uri.with({ scheme, query: '' }));

			let text = '';
			let lastPosition = new cypher-ide.Position(0, 0);

			ranges.forEach(rangeObj => {
				const [conflictRange, fullRange] = rangeObj;
				const [start, end] = conflictRange;
				const [fullStart, fullEnd] = fullRange;

				text += document.getText(new cypher-ide.Range(lastPosition.line, lastPosition.character, fullStart.line, fullStart.character));
				text += document.getText(new cypher-ide.Range(start.line, start.character, end.line, end.character));
				lastPosition = new cypher-ide.Position(fullEnd.line, fullEnd.character);
			});

			const documentEnd = document.lineAt(document.lineCount - 1).range.end;
			text += document.getText(new cypher-ide.Range(lastPosition.line, lastPosition.character, documentEnd.line, documentEnd.character));

			return text;
		}
		catch (ex) {
			await cypher-ide.window.showErrorMessage('Unable to show comparison');
			return null;
		}
	}
}