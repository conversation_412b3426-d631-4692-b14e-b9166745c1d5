/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Cypher IDE Team. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cypher-ide from 'cypher-ide';

export class UriEventHandler extends cypher-ide.EventEmitter<cypher-ide.Uri> implements cypher-ide.UriHandler {
	private _disposable = cypher-ide.window.registerUriHandler(this);

	handleUri(uri: cypher-ide.Uri) {
		this.fire(uri);
	}

	override dispose(): void {
		super.dispose();
		this._disposable.dispose();
	}
}
